"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.documentDbService = exports.DocumentDbService = void 0;
const supabaseService_1 = require("./supabaseService");
class DocumentDbService {
    async createDocument(documentData) {
        const { data, error } = await supabaseService_1.supabase
            .from('documents')
            .insert(documentData)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create document: ${error.message}`);
        }
        return data;
    }
    async getUserDocuments(userId, limit = 50, offset = 0) {
        const { data, error } = await supabaseService_1.supabase
            .from('documents')
            .select('id, user_id, filename, file_type, file_size, supabase_storage_path, uploaded_at, is_processed, processing_error')
            .eq('user_id', userId)
            .order('uploaded_at', { ascending: false })
            .range(offset, offset + limit - 1);
        if (error) {
            throw new Error(`Failed to get documents: ${error.message}`);
        }
        return (data || []);
    }
    async getDocumentById(documentId, userId) {
        const { data, error } = await supabaseService_1.supabase
            .from('documents')
            .select('*')
            .eq('id', documentId)
            .eq('user_id', userId)
            .single();
        if (error) {
            if (error.code === 'PGRST116')
                return null; // Not found
            throw new Error(`Failed to get document: ${error.message}`);
        }
        return data;
    }
    async getDocumentsByIds(documentIds, userId) {
        const { data, error } = await supabaseService_1.supabase
            .from('documents')
            .select('*')
            .in('id', documentIds)
            .eq('user_id', userId);
        if (error) {
            throw new Error(`Failed to get documents: ${error.message}`);
        }
        return data || [];
    }
    async updateDocument(documentId, userId, updates) {
        const { data, error } = await supabaseService_1.supabase
            .from('documents')
            .update(updates)
            .eq('id', documentId)
            .eq('user_id', userId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update document: ${error.message}`);
        }
        return data;
    }
    async deleteDocument(documentId, userId) {
        const { error } = await supabaseService_1.supabase
            .from('documents')
            .delete()
            .eq('id', documentId)
            .eq('user_id', userId);
        if (error) {
            throw new Error(`Failed to delete document: ${error.message}`);
        }
    }
    async searchDocuments(userId, query, limit = 20) {
        const { data, error } = await supabaseService_1.supabase
            .from('documents')
            .select('id, user_id, filename, file_type, file_size, supabase_storage_path, uploaded_at, is_processed, processing_error')
            .eq('user_id', userId)
            .or(`filename.ilike.%${query}%,content_text.ilike.%${query}%`)
            .order('uploaded_at', { ascending: false })
            .limit(limit);
        if (error) {
            throw new Error(`Failed to search documents: ${error.message}`);
        }
        return (data || []);
    }
}
exports.DocumentDbService = DocumentDbService;
exports.documentDbService = new DocumentDbService();
//# sourceMappingURL=documentDbService.js.map