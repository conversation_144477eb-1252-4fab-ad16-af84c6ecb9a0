import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { checkCredits } from '../middleware/creditCheck';
import {
  generateFlashcards,
  generateMoreFlashcards,
  generateQuiz,
  generateMoreQuizQuestions,
  testAIConnection,
  getStudySets,
  getStudySetContent
} from '../controllers/aiController';

const router = express.Router();

// Test AI service connectivity (no auth required for health check)
router.get('/test-connection', testAIConnection);

// All other routes require authentication
router.use(authenticateToken);

// AI Generation endpoints (require credit check)
router.post('/generate-flashcards', checkCredits('flashcard_generation'), generateFlashcards);
router.post('/generate-more-flashcards', checkCredits('flashcard_generation'), generateMoreFlashcards);
router.post('/generate-quiz', checkCredits('quiz_generation'), generateQuiz);
router.post('/generate-more-quiz-questions', checkCredits('quiz_generation'), generateMoreQuizQuestions);

// Study set management endpoints
router.get('/study-sets', getStudySets);
router.get('/study-sets/:studySetId', getStudySetContent);

export default router;
