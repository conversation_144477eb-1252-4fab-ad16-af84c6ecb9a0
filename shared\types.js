// ----------------------------------------
// User & Authentication Types
// ----------------------------------------
// ----------------------------------------
// Difficulty and Content Length Enums
// ----------------------------------------
export var DifficultyLevel;
(function (DifficultyLevel) {
    DifficultyLevel["EASY"] = "easy";
    DifficultyLevel["MEDIUM"] = "medium";
    DifficultyLevel["HARD"] = "hard";
    DifficultyLevel["COLLEGE"] = "college";
    DifficultyLevel["GRADUATE"] = "graduate";
    DifficultyLevel["PHD"] = "phd";
})(DifficultyLevel || (DifficultyLevel = {}));
export var ContentLength;
(function (ContentLength) {
    ContentLength["SHORT"] = "short";
    ContentLength["MEDIUM"] = "medium";
    ContentLength["LONG"] = "long";
})(ContentLength || (ContentLength = {}));
// Helper functions for enum conversions
export const difficultyLevelToString = (level) => {
    const labels = {
        [DifficultyLevel.EASY]: 'Easy',
        [DifficultyLevel.MEDIUM]: 'Medium',
        [DifficultyLevel.HARD]: 'Hard',
        [DifficultyLevel.COLLEGE]: 'College',
        [DifficultyLevel.GRADUATE]: 'Graduate',
        [DifficultyLevel.PHD]: 'PhD'
    };
    return labels[level];
};
export const contentLengthToString = (length) => {
    const labels = {
        [ContentLength.SHORT]: 'Short',
        [ContentLength.MEDIUM]: 'Medium',
        [ContentLength.LONG]: 'Long'
    };
    return labels[length];
};
export const stringToDifficultyLevel = (str) => {
    const normalized = str.toLowerCase();
    switch (normalized) {
        case 'easy': return DifficultyLevel.EASY;
        case 'medium': return DifficultyLevel.MEDIUM;
        case 'hard': return DifficultyLevel.HARD;
        case 'college': return DifficultyLevel.COLLEGE;
        case 'graduate': return DifficultyLevel.GRADUATE;
        case 'phd': return DifficultyLevel.PHD;
        default: return DifficultyLevel.MEDIUM;
    }
};
export const stringToContentLength = (str) => {
    const normalized = str.toLowerCase();
    switch (normalized) {
        case 'short': return ContentLength.SHORT;
        case 'medium': return ContentLength.MEDIUM;
        case 'long': return ContentLength.LONG;
        default: return ContentLength.MEDIUM;
    }
};
// Helper functions for number conversions (for backward compatibility)
export const difficultyLevelToNumber = (level) => {
    const mapping = {
        [DifficultyLevel.EASY]: 1,
        [DifficultyLevel.MEDIUM]: 3,
        [DifficultyLevel.HARD]: 4,
        [DifficultyLevel.COLLEGE]: 5,
        [DifficultyLevel.GRADUATE]: 6,
        [DifficultyLevel.PHD]: 7
    };
    return mapping[level];
};
export const numberToDifficultyLevel = (num) => {
    switch (num) {
        case 1: return DifficultyLevel.EASY;
        case 2: return DifficultyLevel.EASY; // Map 2 to easy for backward compatibility
        case 3: return DifficultyLevel.MEDIUM;
        case 4: return DifficultyLevel.HARD;
        case 5: return DifficultyLevel.COLLEGE;
        case 6: return DifficultyLevel.GRADUATE;
        case 7: return DifficultyLevel.PHD;
        default: return DifficultyLevel.MEDIUM;
    }
};
