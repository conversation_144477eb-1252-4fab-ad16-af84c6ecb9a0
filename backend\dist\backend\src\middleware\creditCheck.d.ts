import { Request, Response, NextFunction } from 'express';
export declare const requireCredits: (operationType: string) => (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const checkCredits: (operationType: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
declare global {
    namespace Express {
        interface Request {
            creditOperation?: string;
        }
    }
}
//# sourceMappingURL=creditCheck.d.ts.map