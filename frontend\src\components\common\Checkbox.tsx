import React from 'react';
import { HiCheck } from 'react-icons/hi';

interface CheckboxProps {
  id?: string;
  label?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  error?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  id,
  label,
  checked,
  onChange,
  disabled = false,
  error,
  description,
  size = 'md',
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.checked);
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          checkbox: 'w-4 h-4',
          icon: 'w-3 h-3',
          label: 'text-sm',
          description: 'text-xs'
        };
      case 'lg':
        return {
          checkbox: 'w-6 h-6',
          icon: 'w-4 h-4',
          label: 'text-lg',
          description: 'text-sm'
        };
      case 'md':
      default:
        return {
          checkbox: 'w-5 h-5',
          icon: 'w-3.5 h-3.5',
          label: 'text-base',
          description: 'text-sm'
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={`flex items-start space-x-3 ${className}`}>
      <div className="flex items-center h-5">
        <div className="relative">
          <input
            id={checkboxId}
            type="checkbox"
            checked={checked}
            onChange={handleChange}
            disabled={disabled}
            className="sr-only"
            aria-describedby={description ? `${checkboxId}-description` : undefined}
          />
          <div
            className={`
              ${sizeClasses.checkbox}
              border-2 rounded-md cursor-pointer transition-all duration-200
              ${checked
                ? 'bg-primary-500 border-primary-500'
                : 'bg-background-secondary border-gray-600 hover:border-gray-500'
              }
              ${disabled
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:shadow-sm'
              }
              ${error
                ? 'border-red-500'
                : ''
              }
              flex items-center justify-center
            `}
            onClick={() => !disabled && onChange(!checked)}
          >
            {checked && (
              <HiCheck 
                className={`${sizeClasses.icon} text-white`}
                aria-hidden="true"
              />
            )}
          </div>
        </div>
      </div>

      {(label || description) && (
        <div className="flex-1 min-w-0">
          {label && (
            <label
              htmlFor={checkboxId}
              className={`
                ${sizeClasses.label}
                font-medium cursor-pointer
                ${disabled
                  ? 'text-gray-500'
                  : 'text-gray-300 hover:text-white'
                }
                ${error
                  ? 'text-red-400'
                  : ''
                }
                block
              `}
            >
              {label}
            </label>
          )}
          
          {description && (
            <p
              id={`${checkboxId}-description`}
              className={`
                ${sizeClasses.description}
                ${disabled
                  ? 'text-gray-600'
                  : 'text-gray-400'
                }
                mt-1
              `}
            >
              {description}
            </p>
          )}
          
          {error && (
            <p className="mt-1 text-sm text-red-500">
              {error}
            </p>
          )}
        </div>
      )}
    </div>
  );
};
