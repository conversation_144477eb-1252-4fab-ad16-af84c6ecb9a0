"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const userSettingsController_1 = require("../controllers/userSettingsController");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticateToken);
// GET /api/user/preferences - Get user preferences
router.get('/', userSettingsController_1.getUserPreferences);
// PUT /api/user/preferences - Update user preferences
router.put('/', userSettingsController_1.updateUserPreferences);
exports.default = router;
//# sourceMappingURL=userPreferences.js.map