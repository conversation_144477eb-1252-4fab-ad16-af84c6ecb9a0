"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const userSettingsController_1 = require("../controllers/userSettingsController");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticateToken);
// GET /api/user/settings - Get user settings
router.get('/', userSettingsController_1.getUserSettings);
// PATCH /api/user/settings - Update user settings
router.patch('/', userSettingsController_1.updateUserSettings);
exports.default = router;
//# sourceMappingURL=userSettings.js.map