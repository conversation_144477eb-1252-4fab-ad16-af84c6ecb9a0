import { QuizQuestion } from '../../../shared/types';
type QuizQuestionData = Omit<QuizQuestion, 'id' | 'study_set_id' | 'times_attempted' | 'times_correct'>;
export declare class QuizService {
    getQuestionsByStudySet(studySetId: string, userId: string): Promise<QuizQuestion[]>;
    createQuizQuestion(studySetId: string, userId: string, questionData: QuizQuestionData): Promise<QuizQuestion>;
    updateQuizQuestion(questionId: string, userId: string, updates: Partial<QuizQuestionData>): Promise<QuizQuestion>;
    deleteQuizQuestion(questionId: string, userId: string): Promise<void>;
    recordQuizAttempt(questionId: string, _userId: string, isCorrect: boolean): Promise<QuizQuestion>;
    getQuizStatistics(studySetId: string, userId: string): Promise<{
        totalQuestions: number;
        totalAttempts: number;
        correctAnswers: number;
        averageScore: number;
        questionStats: Array<{
            id: string;
            question_text: string;
            times_attempted: number;
            times_correct: number;
            success_rate: number;
        }>;
    }>;
}
export declare const quizService: QuizService;
export {};
//# sourceMappingURL=quizService.d.ts.map