export declare class DocumentProcessor {
    processFile(file: Express.Multer.File): Promise<string>;
    private processPDF;
    private processDocx;
    private processText;
    private processPPTX;
    private cleanText;
}
export declare class FileUploadService {
    uploadFile(fileName: string, fileBuffer: Buffer, mimeType: string): Promise<string>;
    deleteFile(filePath: string): Promise<void>;
    getFileUrl(filePath: string): Promise<string>;
}
//# sourceMappingURL=documentService.d.ts.map