"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.supabaseService = exports.SupabaseService = exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
exports.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseServiceKey);
class SupabaseService {
    async createUserProfile(userId, email, name) {
        const { data, error } = await exports.supabase
            .from("users")
            .insert({
            id: userId,
            email,
            name,
            subscription_tier: "Study Starter",
            credits_remaining: 500,
            is_active: true,
        })
            .select()
            .single();
        if (error || !data) {
            throw new Error(error?.message ?? "Failed to create user profile");
        }
        return data;
    }
    async getUserProfile(userId) {
        const { data, error } = await exports.supabase
            .from("users")
            .select("*")
            .eq("id", userId)
            .single();
        if (error) {
            if (error.code === "PGRST116")
                return null;
            throw new Error(error.message);
        }
        return data;
    }
    async updateUserProfile(userId, updates) {
        const { data, error } = await exports.supabase
            .from("users")
            .update(updates)
            .eq("id", userId)
            .select()
            .single();
        if (error || !data) {
            throw new Error(error?.message ?? "Failed to update user profile");
        }
        return data;
    }
    // Verify JWT token and get user
    async verifyToken(token) {
        return await exports.supabase.auth.getUser(token);
    }
}
exports.SupabaseService = SupabaseService;
exports.supabaseService = new SupabaseService();
//# sourceMappingURL=supabaseService.js.map