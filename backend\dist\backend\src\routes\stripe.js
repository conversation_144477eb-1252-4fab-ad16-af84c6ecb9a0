"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const stripeService_1 = require("../services/stripeService");
const auth_1 = require("../middleware/auth");
const supabaseService_1 = require("../services/supabaseService");
const router = express_1.default.Router();
// Create or get Stripe customer
router.post('/customer', auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user?.id;
        const { email, name } = req.body;
        if (!userId || !email) {
            return res.status(400).json({ error: 'User ID and email are required' });
        }
        const customerId = await stripeService_1.stripeService.createOrGetCustomer(userId, email, name);
        res.json({ customerId });
    }
    catch (error) {
        console.error('Error creating/getting customer:', error);
        res.status(500).json({ error: 'Failed to create or get customer' });
    }
});
// Create subscription
router.post('/subscription', auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user?.id;
        const { priceId, email, name } = req.body;
        if (!userId || !priceId || !email) {
            return res.status(400).json({ error: 'User ID, price ID, and email are required' });
        }
        const customerId = await stripeService_1.stripeService.createOrGetCustomer(userId, email, name);
        const subscription = await stripeService_1.stripeService.createSubscription(customerId, priceId);
        res.json({ subscription });
    }
    catch (error) {
        console.error('Error creating subscription:', error);
        res.status(500).json({ error: 'Failed to create subscription' });
    }
});
// Get user subscriptions
router.get('/subscriptions', auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(400).json({ error: 'User ID is required' });
        }
        // Get user's Stripe customer ID
        const { data: profile } = await supabaseService_1.supabase
            .from('users')
            .select('stripe_customer_id')
            .eq('id', userId)
            .single();
        if (!profile?.stripe_customer_id) {
            return res.json({ subscriptions: [] });
        }
        const subscriptions = await stripeService_1.stripeService.getCustomerSubscriptions(profile.stripe_customer_id);
        res.json({ subscriptions });
    }
    catch (error) {
        console.error('Error getting subscriptions:', error);
        res.status(500).json({ error: 'Failed to get subscriptions' });
    }
});
// Cancel subscription
router.post('/subscription/:id/cancel', auth_1.authenticateToken, async (req, res) => {
    try {
        const { id: subscriptionId } = req.params;
        const userId = req.user?.id;
        if (!userId || !subscriptionId) {
            return res.status(400).json({ error: 'User ID and subscription ID are required' });
        }
        // Verify subscription belongs to user
        const { data: profile } = await supabaseService_1.supabase
            .from('users')
            .select('stripe_customer_id')
            .eq('id', userId)
            .single();
        if (!profile?.stripe_customer_id) {
            return res.status(404).json({ error: 'Customer not found' });
        }
        const subscriptions = await stripeService_1.stripeService.getCustomerSubscriptions(profile.stripe_customer_id);
        const subscription = subscriptions.find(sub => sub.id === subscriptionId);
        if (!subscription) {
            return res.status(404).json({ error: 'Subscription not found' });
        }
        const canceledSubscription = await stripeService_1.stripeService.cancelSubscription(subscriptionId);
        res.json({ subscription: canceledSubscription });
    }
    catch (error) {
        console.error('Error canceling subscription:', error);
        res.status(500).json({ error: 'Failed to cancel subscription' });
    }
});
// Create payment intent for credit purchase
router.post('/credit-purchase', auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user?.id;
        const { amount, credits, email, name } = req.body;
        if (!userId || !amount || !credits || !email) {
            return res.status(400).json({ error: 'User ID, amount, credits, and email are required' });
        }
        const customerId = await stripeService_1.stripeService.createOrGetCustomer(userId, email, name);
        const paymentIntent = await stripeService_1.stripeService.createCreditPurchaseIntent(customerId, amount, credits);
        res.json({
            clientSecret: paymentIntent.client_secret,
            paymentIntentId: paymentIntent.id
        });
    }
    catch (error) {
        console.error('Error creating credit purchase:', error);
        res.status(500).json({ error: 'Failed to create credit purchase' });
    }
});
// Get customer invoices
router.get('/invoices', auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user?.id;
        const limit = parseInt(req.query.limit) || 10;
        if (!userId) {
            return res.status(400).json({ error: 'User ID is required' });
        }
        // Get user's Stripe customer ID
        const { data: profile } = await supabaseService_1.supabase
            .from('users')
            .select('stripe_customer_id')
            .eq('id', userId)
            .single();
        if (!profile?.stripe_customer_id) {
            return res.json({ invoices: [] });
        }
        const invoices = await stripeService_1.stripeService.getCustomerInvoices(profile.stripe_customer_id, limit);
        res.json({ invoices });
    }
    catch (error) {
        console.error('Error getting invoices:', error);
        res.status(500).json({ error: 'Failed to get invoices' });
    }
});
// Get customer payment methods
router.get('/payment-methods', auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(400).json({ error: 'User ID is required' });
        }
        // Get user's Stripe customer ID
        const { data: profile } = await supabaseService_1.supabase
            .from('users')
            .select('stripe_customer_id')
            .eq('id', userId)
            .single();
        if (!profile?.stripe_customer_id) {
            return res.json({ paymentMethods: [] });
        }
        const paymentMethods = await stripeService_1.stripeService.getCustomerPaymentMethods(profile.stripe_customer_id);
        res.json({ paymentMethods });
    }
    catch (error) {
        console.error('Error getting payment methods:', error);
        res.status(500).json({ error: 'Failed to get payment methods' });
    }
});
// Get Stripe prices
router.get('/prices', async (_req, res) => {
    try {
        const prices = await stripeService_1.stripeService.getPrices();
        res.json({ prices });
    }
    catch (error) {
        console.error('Error getting prices:', error);
        res.status(500).json({ error: 'Failed to get prices' });
    }
});
// Webhook endpoint for Stripe events
router.post('/webhook', express_1.default.raw({ type: 'application/json' }), async (req, res) => {
    try {
        const signature = req.headers['stripe-signature'];
        if (!signature) {
            return res.status(400).json({ error: 'Missing stripe-signature header' });
        }
        await stripeService_1.stripeService.handleWebhook(req.body, signature);
        res.json({ received: true });
    }
    catch (error) {
        console.error('Webhook error:', error);
        res.status(400).json({ error: 'Webhook signature verification failed' });
    }
});
exports.default = router;
//# sourceMappingURL=stripe.js.map