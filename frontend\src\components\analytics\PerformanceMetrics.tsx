import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  HiTrendingUp,
  HiTrendingDown,
  HiCheckCircle,
  HiLightningBolt,
  HiChartBar
} from 'react-icons/hi';
import { StudySet } from '../../../../shared/types';

interface StudySession {
  id: string;
  studySetId: string;
  type: 'flashcards' | 'quiz';
  startTime: Date;
  endTime?: Date;
  totalItems: number;
  reviewedItems: number;
  flaggedItems: number;
  correctAnswers?: number;
  timeSpent: number;
}

interface PerformanceMetricsProps {
  studySets: StudySet[];
  sessions: StudySession[];
  timeRange: '7d' | '30d' | '90d' | 'all';
  isLoading: boolean;
}

interface PerformanceData {
  overallAccuracy: number;
  accuracyTrend: number;
  averageSpeed: number; // items per minute
  speedTrend: number;
  consistencyScore: number;
  improvementRate: number;
  strongestSubjects: Array<{ name: string; accuracy: number }>;
  weakestSubjects: Array<{ name: string; accuracy: number }>;
  dailyPerformance: Array<{ date: string; accuracy: number; speed: number }>;
}

const formatSpeed = (itemsPerMinute: number): string => {
  if (itemsPerMinute < 1) {
    return `${Math.round(itemsPerMinute * 60)}s/item`;
  }
  return `${itemsPerMinute.toFixed(1)} items/min`;
};

const PerformanceCard: React.FC<{
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: number;
  isLoading?: boolean;
  color?: 'primary' | 'green' | 'red' | 'yellow';
}> = ({ title, value, subtitle, icon: Icon, trend, isLoading, color = 'primary' }) => {
  const colorClasses = {
    primary: 'bg-primary-500/20 text-primary-400',
    green: 'bg-green-500/20 text-green-400',
    red: 'bg-red-500/20 text-red-400',
    yellow: 'bg-yellow-500/20 text-yellow-400'
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-background-secondary rounded-lg p-6 border border-border-primary"
    >
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon className="w-6 h-6" />
        </div>
        
        {trend !== undefined && (
          <div className={`flex items-center space-x-1 ${
            trend >= 0 ? 'text-green-400' : 'text-red-400'
          }`}>
            {trend >= 0 ? (
              <HiTrendingUp className="w-4 h-4" />
            ) : (
              <HiTrendingDown className="w-4 h-4" />
            )}
            <span className="text-sm font-medium">{Math.abs(trend).toFixed(1)}%</span>
          </div>
        )}
      </div>

      <div>
        <h3 className="text-sm font-medium text-gray-400 mb-1">{title}</h3>
        {isLoading ? (
          <div className="animate-pulse">
            <div className="h-8 w-20 bg-gray-600 rounded"></div>
          </div>
        ) : (
          <p className="text-2xl font-bold text-white">{value}</p>
        )}
        {subtitle && (
          <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
        )}
      </div>
    </motion.div>
  );
};

const SubjectPerformanceChart: React.FC<{
  subjects: Array<{ name: string; accuracy: number }>;
  title: string;
  color: string;
}> = ({ subjects, title, color }) => (
  <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
    <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
    <div className="space-y-3">
      {subjects.length === 0 ? (
        <p className="text-gray-400 text-center py-4">No data available</p>
      ) : (
        subjects.map((subject, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-gray-300 truncate flex-1 mr-4">{subject.name}</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${color}`}
                  style={{ width: `${subject.accuracy}%` }}
                />
              </div>
              <span className="text-white font-medium w-12 text-right">
                {Math.round(subject.accuracy)}%
              </span>
            </div>
          </div>
        ))
      )}
    </div>
  </div>
);

export const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  studySets,
  sessions,
  timeRange,
  isLoading
}) => {
  const performanceData = useMemo((): PerformanceData => {
    const now = new Date();
    const timeRangeMs = {
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
      'all': Infinity
    }[timeRange];

    const filteredSessions = sessions.filter(session => {
      const sessionTime = new Date(session.startTime).getTime();
      return now.getTime() - sessionTime <= timeRangeMs;
    });

    // Calculate overall accuracy
    const quizSessions = filteredSessions.filter(s => s.type === 'quiz' && s.correctAnswers !== undefined);
    const totalQuizItems = quizSessions.reduce((sum, session) => sum + session.totalItems, 0);
    const totalCorrect = quizSessions.reduce((sum, session) => sum + (session.correctAnswers || 0), 0);
    const overallAccuracy = totalQuizItems > 0 ? (totalCorrect / totalQuizItems) * 100 : 0;

    // Calculate average speed (items per minute)
    const totalItems = filteredSessions.reduce((sum, session) => sum + session.reviewedItems, 0);
    const totalTimeMinutes = filteredSessions.reduce((sum, session) => sum + session.timeSpent, 0) / 60;
    const averageSpeed = totalTimeMinutes > 0 ? totalItems / totalTimeMinutes : 0;

    // Calculate subject performance
    const subjectPerformance = new Map<string, { correct: number; total: number }>();
    
    quizSessions.forEach(session => {
      const studySet = studySets.find(set => set.id === session.studySetId);
      if (studySet) {
        const existing = subjectPerformance.get(studySet.name) || { correct: 0, total: 0 };
        subjectPerformance.set(studySet.name, {
          correct: existing.correct + (session.correctAnswers || 0),
          total: existing.total + session.totalItems
        });
      }
    });

    const subjectAccuracies = Array.from(subjectPerformance.entries())
      .map(([name, data]) => ({
        name,
        accuracy: data.total > 0 ? (data.correct / data.total) * 100 : 0
      }))
      .sort((a, b) => b.accuracy - a.accuracy);

    const strongestSubjects = subjectAccuracies.slice(0, 5);
    const weakestSubjects = subjectAccuracies.slice(-5).reverse();

    // Calculate daily performance for the last 7 days
    const dailyPerformance = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      const dateStr = date.toISOString().split('T')[0];
      
      const daySessions = filteredSessions.filter(session => {
        const sessionDate = new Date(session.startTime).toISOString().split('T')[0];
        return sessionDate === dateStr;
      });

      const dayQuizSessions = daySessions.filter(s => s.type === 'quiz' && s.correctAnswers !== undefined);
      const dayTotalItems = dayQuizSessions.reduce((sum, session) => sum + session.totalItems, 0);
      const dayCorrect = dayQuizSessions.reduce((sum, session) => sum + (session.correctAnswers || 0), 0);
      const dayAccuracy = dayTotalItems > 0 ? (dayCorrect / dayTotalItems) * 100 : 0;

      const dayReviewedItems = daySessions.reduce((sum, session) => sum + session.reviewedItems, 0);
      const dayTimeMinutes = daySessions.reduce((sum, session) => sum + session.timeSpent, 0) / 60;
      const daySpeed = dayTimeMinutes > 0 ? dayReviewedItems / dayTimeMinutes : 0;

      return {
        date: date.toLocaleDateString('en-US', { weekday: 'short' }),
        accuracy: dayAccuracy,
        speed: daySpeed
      };
    });

    // Calculate trends (simplified)
    const recentAccuracy = dailyPerformance.slice(-3).reduce((sum, day) => sum + day.accuracy, 0) / 3;
    const earlierAccuracy = dailyPerformance.slice(0, 3).reduce((sum, day) => sum + day.accuracy, 0) / 3;
    const accuracyTrend = earlierAccuracy > 0 ? ((recentAccuracy - earlierAccuracy) / earlierAccuracy) * 100 : 0;

    const recentSpeed = dailyPerformance.slice(-3).reduce((sum, day) => sum + day.speed, 0) / 3;
    const earlierSpeed = dailyPerformance.slice(0, 3).reduce((sum, day) => sum + day.speed, 0) / 3;
    const speedTrend = earlierSpeed > 0 ? ((recentSpeed - earlierSpeed) / earlierSpeed) * 100 : 0;

    // Calculate consistency score (lower variance = higher consistency)
    const accuracyVariance = dailyPerformance.reduce((sum, day) => {
      const diff = day.accuracy - overallAccuracy;
      return sum + (diff * diff);
    }, 0) / dailyPerformance.length;
    const consistencyScore = Math.max(0, 100 - Math.sqrt(accuracyVariance));

    return {
      overallAccuracy,
      accuracyTrend,
      averageSpeed,
      speedTrend,
      consistencyScore,
      improvementRate: accuracyTrend,
      strongestSubjects,
      weakestSubjects,
      dailyPerformance
    };
  }, [sessions, studySets, timeRange]);

  return (
    <div className="space-y-6">
      {/* Main Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <PerformanceCard
          title="Overall Accuracy"
          value={`${Math.round(performanceData.overallAccuracy)}%`}
          subtitle="Quiz performance"
          icon={HiCheckCircle}
          trend={performanceData.accuracyTrend}
          isLoading={isLoading}
          color="green"
        />
        
        <PerformanceCard
          title="Average Speed"
          value={formatSpeed(performanceData.averageSpeed)}
          subtitle="Items per minute"
          icon={HiLightningBolt}
          trend={performanceData.speedTrend}
          isLoading={isLoading}
          color="yellow"
        />
        
        <PerformanceCard
          title="Consistency"
          value={`${Math.round(performanceData.consistencyScore)}%`}
          subtitle="Performance stability"
          icon={HiChartBar}
          isLoading={isLoading}
          color="primary"
        />
        
        <PerformanceCard
          title="Improvement"
          value={`${performanceData.improvementRate >= 0 ? '+' : ''}${performanceData.improvementRate.toFixed(1)}%`}
          subtitle="Recent trend"
          icon={HiTrendingUp}
          isLoading={isLoading}
          color={performanceData.improvementRate >= 0 ? 'green' : 'red'}
        />
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SubjectPerformanceChart
          subjects={performanceData.strongestSubjects}
          title="Strongest Subjects"
          color="bg-green-500"
        />
        
        <SubjectPerformanceChart
          subjects={performanceData.weakestSubjects}
          title="Areas for Improvement"
          color="bg-red-500"
        />
      </div>

      {/* Daily Performance Chart */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h3 className="text-lg font-semibold text-white mb-4">Daily Performance (Last 7 Days)</h3>
        <div className="flex items-end justify-between h-32 space-x-2">
          {performanceData.dailyPerformance.map((day, index) => {
            const maxAccuracy = Math.max(...performanceData.dailyPerformance.map(d => d.accuracy), 1);
            const height = (day.accuracy / maxAccuracy) * 100;
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full bg-gray-700 rounded-t relative" style={{ height: '100px' }}>
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: `${height}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-gradient-to-t from-green-500 to-green-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0"
                    title={`${Math.round(day.accuracy)}% accuracy, ${formatSpeed(day.speed)}`}
                  />
                </div>
                <div className="text-xs text-gray-400 mt-2">{day.date}</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
