import { Request, Response } from 'express';
export declare const getCreditBalance: (req: Request, res: Response) => Promise<void>;
export declare const getCreditHistory: (req: Request, res: Response) => Promise<void>;
export declare const getCreditStats: (req: Request, res: Response) => Promise<void>;
export declare const getOperationCosts: (_req: Request, res: Response) => Promise<void>;
export declare const checkCreditSufficiency: (req: Request, res: Response) => Promise<void>;
export declare const purchaseCredits: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=creditController.d.ts.map