import { Request, Response } from 'express';
export declare const getQuizQuestions: (req: Request, res: Response) => Promise<void>;
export declare const createQuizQuestion: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateQuizQuestion: (req: Request, res: Response) => Promise<void>;
export declare const deleteQuizQuestion: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=quizQuestionController.d.ts.map