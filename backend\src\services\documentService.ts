import pdf from 'pdf-parse';
import mammoth from 'mammoth';
import { createClient } from '@supabase/supabase-js';

// Create a service role client for storage operations (bypasses RLS)
const supabaseServiceRole = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export class DocumentProcessor {
  async processFile(file: Express.Multer.File): Promise<string> {
    switch (file.mimetype) {
      case 'application/pdf':
        return this.processPDF(file.buffer);
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.processDocx(file.buffer);
      case 'text/plain':
        return this.processText(file.buffer);
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return this.processPPTX(file.buffer);
      default:
        throw new Error('Unsupported file type');
    }
  }

  private async processPDF(buffer: Buffer): Promise<string> {
    try {
      const data = await pdf(buffer);
      return this.cleanText(data.text);
    } catch (error: any) {
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  private async processDocx(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return this.cleanText(result.value);
    } catch (error: any) {
      throw new Error(`DOCX processing failed: ${error.message}`);
    }
  }

  private async processText(buffer: Buffer): Promise<string> {
    try {
      const text = buffer.toString('utf-8');
      return this.cleanText(text);
    } catch (error: any) {
      throw new Error(`Text processing failed: ${error.message}`);
    }
  }

  private async processPPTX(buffer: Buffer): Promise<string> {
    // For PPTX, we'll use a simple text extraction
    // In production, consider using a library like 'officegen' or 'node-pptx'
    try {
      // Basic PPTX text extraction (simplified)
      const text = buffer.toString('utf-8');
      // Extract readable text from PPTX XML structure
      const textMatches = text.match(/<a:t[^>]*>([^<]+)<\/a:t>/g);
      if (textMatches) {
        const extractedText = textMatches
          .map(match => match.replace(/<[^>]+>/g, ''))
          .join(' ');
        return this.cleanText(extractedText);
      }
      return 'Unable to extract text from PowerPoint file';
    } catch (error: any) {
      throw new Error(`PPTX processing failed: ${error.message}`);
    }
  }

  private cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n\s*\n/g, '\n') // Remove empty lines
      .trim();
  }
}

// File upload service for Supabase Storage
export class FileUploadService {
  async uploadFile(fileName: string, fileBuffer: Buffer, mimeType: string): Promise<string> {
    try {
      // Use service role client to bypass RLS for storage operations
      const { data, error } = await supabaseServiceRole.storage
        .from('documents')
        .upload(fileName, fileBuffer, {
          contentType: mimeType,
          upsert: false
        });

      if (error) {
        throw new Error(`File upload failed: ${error.message}`);
      }

      return data.path;
    } catch (error: any) {
      throw new Error(`Storage upload failed: ${error.message}`);
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      const { error } = await supabaseServiceRole.storage
        .from('documents')
        .remove([filePath]);

      if (error) {
        throw new Error(`File deletion failed: ${error.message}`);
      }
    } catch (error) {
      console.error('File deletion error:', error);
      // Don't throw here as this is cleanup
    }
  }

  async getFileUrl(filePath: string): Promise<string> {
    try {
      const { data } = await supabaseServiceRole.storage
        .from('documents')
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (!data?.signedUrl) {
        throw new Error('Failed to generate file URL');
      }

      return data.signedUrl;
    } catch (error: any) {
      throw new Error(`URL generation failed: ${error.message}`);
    }
  }
}
