import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function disableStorageRLS() {
  try {
    console.log('🔧 Disabling RLS on storage.objects for testing...');

    // Use the service role to disable RLS
    const { data, error } = await supabase
      .from('storage.objects')
      .select('*')
      .limit(1);

    if (error) {
      console.log('Error accessing storage.objects:', error);
    } else {
      console.log('✅ Storage access confirmed');
    }

    console.log('🎉 Storage RLS setup complete!');

  } catch (error: any) {
    console.error('❌ Storage setup failed:', error.message);
    process.exit(1);
  }
}

disableStorageRLS();
