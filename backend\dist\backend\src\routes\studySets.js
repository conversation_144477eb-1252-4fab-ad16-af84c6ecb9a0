"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const studySetController_1 = require("../controllers/studySetController");
const router = (0, express_1.Router)();
// All study set routes require authentication
router.use(auth_1.authenticateToken);
// Study set CRUD operations
router.get('/', studySetController_1.getStudySets);
router.post('/', studySetController_1.createStudySet);
router.get('/:id', studySetController_1.getStudySet);
router.put('/:id', studySetController_1.updateStudySet);
router.delete('/:id', studySetController_1.deleteStudySet);
// Study set content and progress
router.get('/:id/content', studySetController_1.getStudySetContent);
router.post('/:id/study', studySetController_1.updateStudyProgress);
exports.default = router;
//# sourceMappingURL=studySets.js.map