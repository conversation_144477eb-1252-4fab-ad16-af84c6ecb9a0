import { CreditTransaction, AIOperationCost } from '../../../shared/types';
export declare class CreditService {
    getOperationCost(operationType: string): Promise<number>;
    getOperationsPerCredit(operationType: string): Promise<number>;
    getEffectiveCreditCost(operationType: string, operationCount?: number): Promise<number>;
    getAllOperationCosts(): Promise<AIOperationCost[]>;
    deductCredits(userId: string, operationType: string, metadata?: any, studySetId?: string): Promise<{
        success: boolean;
        remainingCredits: number;
        message: string;
    }>;
    addCredits(userId: string, creditsToAdd: number, source: string, referenceId?: string): Promise<{
        success: boolean;
        newBalance: number;
        message: string;
    }>;
    getUserCredits(userId: string): Promise<number>;
    validateCreditOperation(userId: string, operationType: string, operationCount?: number): Promise<boolean>;
    getCreditHistory(userId: string, limit?: number, offset?: number): Promise<{
        transactions: CreditTransaction[];
        total: number;
    }>;
    getCreditStats(userId: string, days?: number): Promise<{
        totalUsed: number;
        totalAdded: number;
        operationBreakdown: {
            operation_type: string;
            credits_used: number;
            count: number;
        }[];
    }>;
    checkSufficientCredits(userId: string, operationType: string, operationCount?: number): Promise<{
        sufficient: boolean;
        currentCredits: number;
        requiredCredits: number;
        shortfall: number;
    }>;
}
export declare class CreditError extends Error {
    originalError?: any | undefined;
    constructor(message: string, originalError?: any | undefined);
}
export declare const creditService: CreditService;
//# sourceMappingURL=creditService.d.ts.map