import { UserProfile } from "../../../shared/types";
export declare const supabase: import("@supabase/supabase-js").SupabaseClient<any, "public", any>;
export declare class SupabaseService {
    createUserProfile(userId: string, email: string, name?: string): Promise<UserProfile>;
    getUserProfile(userId: string): Promise<UserProfile | null>;
    updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile>;
    verifyToken(token: string): Promise<import("@supabase/supabase-js").UserResponse>;
}
export declare const supabaseService: SupabaseService;
//# sourceMappingURL=supabaseService.d.ts.map