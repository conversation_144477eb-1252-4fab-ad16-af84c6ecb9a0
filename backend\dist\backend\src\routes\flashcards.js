"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const flashcardController_1 = require("../controllers/flashcardController");
const router = (0, express_1.Router)();
// All flashcard routes require authentication
router.use(auth_1.authenticateToken);
// Flashcard CRUD operations
router.get('/study-set/:studySetId', flashcardController_1.getFlashcards);
router.post('/study-set/:studySetId', flashcardController_1.createFlashcard);
router.put('/:id', flashcardController_1.updateFlashcard);
router.delete('/:id', flashcardController_1.deleteFlashcard);
router.patch('/:id/flag', flashcardController_1.toggleFlashcardFlag);
// Bulk operations
router.post('/bulk-delete', flashcardController_1.bulkDeleteFlashcards);
exports.default = router;
//# sourceMappingURL=flashcards.js.map