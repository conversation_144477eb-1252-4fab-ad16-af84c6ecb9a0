# Phase 7: AI Integration Backend
**Priority**: CRITICAL - Core AI generation functionality
**Dependencies**: Phase 1 (Foundation), Phase 2 (Database), Phase 3 (Authentication), Phase 6 (Credit System)
**Estimated Time**: 4-5 hours

## Overview
Implement AI content generation using OpenRouter API with Gemini-2.0-flash-exp model for creating flashcards and quiz questions from documents.

## Tasks

### 7.1 AI Service Implementation
**File**: `backend/src/services/aiService.ts`

```typescript
import axios from 'axios';
import { FlashcardData, QuizQuestionData, AIGenerationRequest, AIGenerationResult } from '../../../shared/types';

export class AIService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://openrouter.ai/api/v1';
  private readonly model = 'google/gemini-2.0-flash-exp';

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY!;
    if (!this.apiKey) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }
  }

  async generateFlashcards(
    content: string,
    count: number = 10,
    customPrompt?: string
  ): Promise<FlashcardData[]> {
    const prompt = this.buildFlashcardPrompt(content, count, customPrompt);
    
    try {
      const response = await this.callOpenRouter(prompt);
      return this.parseFlashcardResponse(response);
    } catch (error) {
      throw new AIError('Flashcard generation failed', error);
    }
  }

  async generateQuizQuestions(
    content: string,
    count: number = 10,
    customPrompt?: string
  ): Promise<QuizQuestionData[]> {
    const prompt = this.buildQuizPrompt(content, count, customPrompt);
    
    try {
      const response = await this.callOpenRouter(prompt);
      return this.parseQuizResponse(response);
    } catch (error) {
      throw new AIError('Quiz generation failed', error);
    }
  }

  private buildFlashcardPrompt(content: string, count: number, customPrompt?: string): string {
    const basePrompt = `
Create ${count} high-quality flashcards from the following content. Each flashcard should have a clear, concise front (question/term) and a comprehensive back (answer/definition).

Guidelines:
- Focus on key concepts, definitions, and important facts
- Make questions specific and answerable
- Ensure answers are complete but concise
- Vary question types (definitions, explanations, examples, applications)
- Avoid overly simple or overly complex questions
- Use clear, educational language

${customPrompt ? `Additional instructions: ${customPrompt}\n` : ''}

Content to process:
${content.substring(0, 8000)} ${content.length > 8000 ? '...[truncated]' : ''}

Return ONLY a valid JSON array with this exact structure:
[
  {
    "front": "Question or term here",
    "back": "Answer or definition here",
    "difficulty_level": 1-5
  }
]

Ensure the response is valid JSON that can be parsed directly.`;

    return basePrompt;
  }

  private buildQuizPrompt(content: string, count: number, customPrompt?: string): string {
    const basePrompt = `
Create ${count} diverse quiz questions from the following content. Include multiple choice, select all that apply, true/false, and short answer questions.

Guidelines:
- Create a mix of question types
- Ensure all questions are answerable from the content
- Make distractors plausible but clearly incorrect
- Include 3-4 options for multiple choice questions
- Provide clear explanations for answers
- Vary difficulty levels

${customPrompt ? `Additional instructions: ${customPrompt}\n` : ''}

Content to process:
${content.substring(0, 8000)} ${content.length > 8000 ? '...[truncated]' : ''}

Return ONLY a valid JSON array with this exact structure:
[
  {
    "question_text": "Question here",
    "question_type": "multiple_choice|select_all|true_false|short_answer",
    "options": ["Option 1", "Option 2", "Option 3", "Option 4"] or null for short_answer,
    "correct_answers": ["Correct answer(s)"],
    "explanation": "Explanation of the correct answer",
    "difficulty_level": 1-5
  }
]

Ensure the response is valid JSON that can be parsed directly.`;

    return basePrompt;
  }

  private async callOpenRouter(prompt: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 4000,
          top_p: 0.9
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://chewyai.com',
            'X-Title': 'ChewyAI Study Material Generator'
          },
          timeout: 60000 // 60 second timeout
        }
      );

      if (!response.data?.choices?.[0]?.message?.content) {
        throw new Error('Invalid response from AI service');
      }

      return response.data.choices[0].message.content;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('AI service rate limit exceeded. Please try again later.');
        } else if (error.response?.status === 401) {
          throw new Error('AI service authentication failed');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('AI service request timed out');
        }
      }
      throw new Error(`AI service error: ${error.message}`);
    }
  }

  private parseFlashcardResponse(response: string): FlashcardData[] {
    try {
      // Clean response - remove any markdown formatting
      const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
      
      const flashcards = JSON.parse(cleanResponse);
      
      if (!Array.isArray(flashcards)) {
        throw new Error('Response is not an array');
      }

      return flashcards.map((card, index) => {
        if (!card.front || !card.back) {
          throw new Error(`Invalid flashcard at index ${index}: missing front or back`);
        }
        
        return {
          front: String(card.front).trim(),
          back: String(card.back).trim(),
          difficulty_level: this.validateDifficultyLevel(card.difficulty_level),
          is_ai_generated: true
        };
      });
    } catch (error) {
      console.error('Failed to parse flashcard response:', response);
      throw new Error(`Failed to parse AI response: ${error.message}`);
    }
  }

  private parseQuizResponse(response: string): QuizQuestionData[] {
    try {
      // Clean response - remove any markdown formatting
      const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
      
      const questions = JSON.parse(cleanResponse);
      
      if (!Array.isArray(questions)) {
        throw new Error('Response is not an array');
      }

      return questions.map((question, index) => {
        if (!question.question_text || !question.question_type || !question.correct_answers) {
          throw new Error(`Invalid question at index ${index}: missing required fields`);
        }

        const validTypes = ['multiple_choice', 'select_all', 'true_false', 'short_answer'];
        if (!validTypes.includes(question.question_type)) {
          throw new Error(`Invalid question type at index ${index}: ${question.question_type}`);
        }

        return {
          question_text: String(question.question_text).trim(),
          question_type: question.question_type,
          options: question.options ? question.options.map(opt => String(opt).trim()) : null,
          correct_answers: Array.isArray(question.correct_answers) 
            ? question.correct_answers.map(ans => String(ans).trim())
            : [String(question.correct_answers).trim()],
          explanation: question.explanation ? String(question.explanation).trim() : null,
          difficulty_level: this.validateDifficultyLevel(question.difficulty_level),
          is_ai_generated: true
        };
      });
    } catch (error) {
      console.error('Failed to parse quiz response:', response);
      throw new Error(`Failed to parse AI response: ${error.message}`);
    }
  }

  private validateDifficultyLevel(level: any): number {
    const numLevel = Number(level);
    if (isNaN(numLevel) || numLevel < 1 || numLevel > 5) {
      return 3; // Default to medium difficulty
    }
    return Math.round(numLevel);
  }

  // Test AI service connectivity
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.callOpenRouter('Respond with "OK" if you can read this message.');
      return response.toLowerCase().includes('ok');
    } catch (error) {
      console.error('AI service connection test failed:', error);
      return false;
    }
  }
}

export class AIError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'AIError';
  }
}

export const aiService = new AIService();
```

### 7.2 Study Set Service
**File**: `backend/src/services/studySetService.ts`

```typescript
import { supabase } from './supabaseService';
import { StudySet, FlashcardData, QuizQuestionData, StudySetType } from '../../../shared/types';

export class StudySetService {
  async createStudySet(data: {
    user_id: string;
    name: string;
    type: StudySetType;
    is_ai_generated: boolean;
    source_documents?: { id: string; filename: string }[];
    custom_prompt?: string;
  }): Promise<StudySet> {
    const { data: studySet, error } = await supabase
      .from('study_sets')
      .insert({
        user_id: data.user_id,
        name: data.name,
        type: data.type,
        is_ai_generated: data.is_ai_generated,
        source_documents: data.source_documents || null,
        custom_prompt: data.custom_prompt || null,
        total_items: 0
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create study set: ${error.message}`);
    }

    return studySet;
  }

  async addFlashcardsToSet(studySetId: string, flashcards: FlashcardData[]): Promise<void> {
    const flashcardInserts = flashcards.map(card => ({
      study_set_id: studySetId,
      front: card.front,
      back: card.back,
      difficulty_level: card.difficulty_level,
      is_ai_generated: card.is_ai_generated
    }));

    const { error } = await supabase
      .from('flashcards')
      .insert(flashcardInserts);

    if (error) {
      throw new Error(`Failed to add flashcards: ${error.message}`);
    }
  }

  async addQuizQuestionsToSet(studySetId: string, questions: QuizQuestionData[]): Promise<void> {
    const questionInserts = questions.map(question => ({
      study_set_id: studySetId,
      question_text: question.question_text,
      question_type: question.question_type,
      options: question.options,
      correct_answers: question.correct_answers,
      explanation: question.explanation,
      difficulty_level: question.difficulty_level,
      is_ai_generated: question.is_ai_generated
    }));

    const { error } = await supabase
      .from('quiz_questions')
      .insert(questionInserts);

    if (error) {
      throw new Error(`Failed to add quiz questions: ${error.message}`);
    }
  }

  async getUserStudySets(userId: string, limit = 50, offset = 0): Promise<StudySet[]> {
    const { data, error } = await supabase
      .from('study_sets')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Failed to get study sets: ${error.message}`);
    }

    return data || [];
  }

  async getStudySetById(studySetId: string, userId: string): Promise<StudySet | null> {
    const { data, error } = await supabase
      .from('study_sets')
      .select('*')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw new Error(`Failed to get study set: ${error.message}`);
    }

    return data;
  }

  async deleteStudySet(studySetId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('study_sets')
      .delete()
      .eq('id', studySetId)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to delete study set: ${error.message}`);
    }
  }

  async updateStudySet(studySetId: string, userId: string, updates: Partial<StudySet>): Promise<StudySet> {
    const { data, error } = await supabase
      .from('study_sets')
      .update(updates)
      .eq('id', studySetId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update study set: ${error.message}`);
    }

    return data;
  }
}

export const studySetService = new StudySetService();
```

### 7.3 AI Generation Controller
**File**: `backend/src/controllers/aiController.ts`

```typescript
import { Request, Response } from 'express';
import { aiService } from '../services/aiService';
import { studySetService } from '../services/studySetService';
import { documentDbService } from '../services/documentDbService';
import { creditService } from '../services/creditService';
import { StudySetType } from '../../../shared/types';

export const generateFlashcards = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { documentIds, name, count = 10, customPrompt } = req.body;

    // Validate input
    if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Document IDs are required'
      });
    }

    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Study set name is required'
      });
    }

    // Get documents and combine content
    const documents = await documentDbService.getDocumentsByIds(documentIds, userId);
    
    if (documents.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No documents found'
      });
    }

    const combinedContent = documents
      .map(doc => `${doc.filename}:\n${doc.content_text}`)
      .join('\n\n---\n\n');

    if (combinedContent.trim().length < 100) {
      return res.status(400).json({
        success: false,
        error: 'Document content is too short for AI generation'
      });
    }

    // Deduct credits
    const creditResult = await creditService.deductCredits(
      userId,
      'flashcard_generation',
      { documentIds, count, customPrompt: !!customPrompt }
    );

    if (!creditResult.success) {
      return res.status(402).json({
        success: false,
        error: creditResult.message
      });
    }

    try {
      // Generate flashcards with AI
      const flashcards = await aiService.generateFlashcards(combinedContent, count, customPrompt);

      // Create study set
      const studySet = await studySetService.createStudySet({
        user_id: userId,
        name: name.trim(),
        type: 'flashcards',
        is_ai_generated: true,
        source_documents: documents.map(doc => ({ id: doc.id, filename: doc.filename })),
        custom_prompt: customPrompt || null
      });

      // Add flashcards to study set
      await studySetService.addFlashcardsToSet(studySet.id, flashcards);

      res.status(201).json({
        success: true,
        data: {
          studySet,
          flashcards,
          creditsRemaining: creditResult.remainingCredits
        },
        message: 'Flashcards generated successfully'
      });

    } catch (aiError) {
      // Refund credits if AI generation fails
      await creditService.addCredits(
        userId,
        1,
        'refund_failed_generation',
        `flashcard_generation_${Date.now()}`
      );

      throw aiError;
    }

  } catch (error) {
    console.error('Generate flashcards error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate flashcards'
    });
  }
};

export const generateQuiz = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { documentIds, name, count = 10, customPrompt } = req.body;

    // Validate input
    if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Document IDs are required'
      });
    }

    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Study set name is required'
      });
    }

    // Get documents and combine content
    const documents = await documentDbService.getDocumentsByIds(documentIds, userId);
    
    if (documents.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No documents found'
      });
    }

    const combinedContent = documents
      .map(doc => `${doc.filename}:\n${doc.content_text}`)
      .join('\n\n---\n\n');

    if (combinedContent.trim().length < 100) {
      return res.status(400).json({
        success: false,
        error: 'Document content is too short for AI generation'
      });
    }

    // Deduct credits
    const creditResult = await creditService.deductCredits(
      userId,
      'quiz_generation',
      { documentIds, count, customPrompt: !!customPrompt }
    );

    if (!creditResult.success) {
      return res.status(402).json({
        success: false,
        error: creditResult.message
      });
    }

    try {
      // Generate quiz questions with AI
      const questions = await aiService.generateQuizQuestions(combinedContent, count, customPrompt);

      // Create study set
      const studySet = await studySetService.createStudySet({
        user_id: userId,
        name: name.trim(),
        type: 'quiz',
        is_ai_generated: true,
        source_documents: documents.map(doc => ({ id: doc.id, filename: doc.filename })),
        custom_prompt: customPrompt || null
      });

      // Add questions to study set
      await studySetService.addQuizQuestionsToSet(studySet.id, questions);

      res.status(201).json({
        success: true,
        data: {
          studySet,
          questions,
          creditsRemaining: creditResult.remainingCredits
        },
        message: 'Quiz generated successfully'
      });

    } catch (aiError) {
      // Refund credits if AI generation fails
      await creditService.addCredits(
        userId,
        1,
        'refund_failed_generation',
        `quiz_generation_${Date.now()}`
      );

      throw aiError;
    }

  } catch (error) {
    console.error('Generate quiz error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate quiz'
    });
  }
};

export const testAIConnection = async (req: Request, res: Response) => {
  try {
    const isConnected = await aiService.testConnection();
    
    res.json({
      success: true,
      data: { connected: isConnected },
      message: isConnected ? 'AI service is connected' : 'AI service connection failed'
    });
  } catch (error) {
    console.error('AI connection test error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test AI connection'
    });
  }
};
```

## Acceptance Criteria
- [ ] OpenRouter API integration working with Gemini model
- [ ] Flashcard generation produces valid, educational content
- [ ] Quiz generation creates diverse question types
- [ ] Credit deduction happens atomically before AI calls
- [ ] Credit refunds work when AI generation fails
- [ ] Error handling for AI service failures and timeouts
- [ ] Content validation ensures quality output
- [ ] Study sets created with proper metadata
- [ ] Document content properly combined for AI processing
- [ ] AI responses parsed correctly into database format

## Next Phase Dependencies
- Phase 8 (Study Set Management) requires study set creation functionality
- Phase 9 (Frontend AI Components) requires these API endpoints
- Phase 10 (Study Interfaces) requires generated content retrieval
