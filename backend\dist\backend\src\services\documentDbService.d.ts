import { DocumentMetadata, DocumentWithContent, DocumentFileType } from '../../../shared/types';
export declare class DocumentDbService {
    createDocument(documentData: {
        user_id: string;
        filename: string;
        file_type: DocumentFileType;
        file_size: number;
        content_text: string;
        supabase_storage_path: string;
        is_processed: boolean;
        processing_error?: string;
    }): Promise<DocumentMetadata>;
    getUserDocuments(userId: string, limit?: number, offset?: number): Promise<DocumentMetadata[]>;
    getDocumentById(documentId: string, userId: string): Promise<DocumentWithContent | null>;
    getDocumentsByIds(documentIds: string[], userId: string): Promise<DocumentWithContent[]>;
    updateDocument(documentId: string, userId: string, updates: Partial<DocumentMetadata>): Promise<DocumentMetadata>;
    deleteDocument(documentId: string, userId: string): Promise<void>;
    searchDocuments(userId: string, query: string, limit?: number): Promise<DocumentMetadata[]>;
}
export declare const documentDbService: DocumentDbService;
//# sourceMappingURL=documentDbService.d.ts.map