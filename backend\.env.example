# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Supabase Configuration
SUPABASE_URL=https://jpvbtrzvbpyzgtpvltss.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Stripe Configuration (to be added in Phase 6)
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# OpenRouter AI Configuration (to be added in Phase 7)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# JWT Configuration (to be added in Phase 3)
JWT_SECRET=your_jwt_secret_here

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,docx,txt,pptx
