import { DifficultyLevel } from '../../../shared/types';
export interface UserSettings {
    id: string;
    user_id: string;
    skip_delete_confirmations: boolean;
    shuffle_flashcards: boolean;
    created_at: string;
    updated_at: string;
}
export interface UserSettingsUpdate {
    skip_delete_confirmations?: boolean;
    shuffle_flashcards?: boolean;
}
export interface UserPreferences {
    id: string;
    user_id: string;
    theme: 'dark' | 'light';
    language: string;
    study_reminders: boolean;
    auto_save: boolean;
    default_study_mode: 'flashcards' | 'quiz';
    session_duration: number;
    difficulty_level: DifficultyLevel;
    created_at: string;
    updated_at: string;
}
export interface UserPreferencesUpdate {
    theme?: 'dark' | 'light';
    language?: string;
    study_reminders?: boolean;
    auto_save?: boolean;
    default_study_mode?: 'flashcards' | 'quiz';
    session_duration?: number;
    difficulty_level?: DifficultyLevel;
}
declare class UserSettingsService {
    getUserSettings(userId: string): Promise<UserSettings>;
    createDefaultSettings(userId: string): Promise<UserSettings>;
    updateUserSettings(userId: string, updates: UserSettingsUpdate): Promise<UserSettings>;
    deleteUserSettings(userId: string): Promise<void>;
}
export declare const userSettingsService: UserSettingsService;
declare class UserPreferencesService {
    getUserPreferences(userId: string): Promise<UserPreferences>;
    createDefaultPreferences(userId: string): Promise<UserPreferences>;
    updateUserPreferences(userId: string, updates: UserPreferencesUpdate): Promise<UserPreferences>;
    deleteUserPreferences(userId: string): Promise<void>;
}
export declare const userPreferencesService: UserPreferencesService;
export {};
//# sourceMappingURL=userSettingsService.d.ts.map