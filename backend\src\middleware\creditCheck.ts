import { Request, Response, NextFunction } from 'express';
import { creditService } from '../services/creditService';

// Middleware to check if user has sufficient credits for an operation
export const requireCredits = (operationType: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user!.id;
      
      const hasCredits = await creditService.validateCreditOperation(userId, operationType);
      
      if (!hasCredits) {
        const creditCheck = await creditService.checkSufficientCredits(userId, operationType);
        
        return res.status(402).json({
          success: false,
          error: 'Insufficient credits',
          data: {
            currentCredits: creditCheck.currentCredits,
            requiredCredits: creditCheck.requiredCredits,
            shortfall: creditCheck.shortfall
          }
        });
      }

      // Store operation type in request for later use
      req.creditOperation = operationType;
      next();
    } catch (error) {
      console.error('Credit check error:', error);
      res.status(500).json({
        success: false,
        error: 'Credit validation failed'
      });
    }
  };
};

// Alternative middleware that just checks credits without blocking
export const checkCredits = (operationType: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      req.creditOperation = operationType;
      next();
    } catch (error) {
      console.error('Credit check error:', error);
      res.status(500).json({
        success: false,
        error: 'Credit validation failed'
      });
    }
  };
};

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      creditOperation?: string;
    }
  }
}
