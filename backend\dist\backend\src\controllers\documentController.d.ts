import { Request, Response } from 'express';
export declare const uploadDocument: (import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>> | ((error: any, _req: Request, res: Response, next: any) => Response<any, Record<string, any>> | undefined))[];
export declare const getDocuments: (req: Request, res: Response) => Promise<void>;
export declare const getDocument: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteDocument: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const searchDocuments: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=documentController.d.ts.map