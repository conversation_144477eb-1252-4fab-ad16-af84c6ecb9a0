import express from 'express';
import supabase from '../config/supabase';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get study sessions for analytics
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { timeRange = '30d' } = req.query;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
    }

    // Calculate date filter based on time range
    let dateFilter = '';
    const now = new Date();
    
    switch (timeRange) {
      case '7d':
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFilter = sevenDaysAgo.toISOString();
        break;
      case '30d':
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        dateFilter = thirtyDaysAgo.toISOString();
        break;
      case '90d':
        const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        dateFilter = ninetyDaysAgo.toISOString();
        break;
      case 'all':
        dateFilter = '1970-01-01T00:00:00.000Z'; // Very old date to get all records
        break;
      default:
        const defaultDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        dateFilter = defaultDaysAgo.toISOString();
    }

    // Query study sessions with study set information
    const { data: sessions, error } = await supabase
      .from('study_sessions')
      .select(`
        id,
        study_set_id,
        session_type,
        started_at,
        ended_at,
        total_items,
        completed_items,
        correct_answers,
        time_spent_seconds,
        session_data,
        study_sets (
          id,
          name,
          type
        )
      `)
      .eq('user_id', userId)
      .gte('started_at', dateFilter)
      .order('started_at', { ascending: false });

    if (error) {
      console.error('Error fetching study sessions:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch study sessions'
      });
    }

    // Transform the data to match frontend expectations
    const transformedSessions = sessions?.map(session => ({
      id: session.id,
      studySetId: session.study_set_id,
      studySetName: (session.study_sets as any)?.name || 'Unknown Study Set',
      type: session.session_type,
      startTime: session.started_at,
      endTime: session.ended_at,
      totalItems: session.total_items,
      completedItems: session.completed_items,
      correctAnswers: session.correct_answers,
      timeSpent: session.time_spent_seconds,
      sessionData: session.session_data
    })) || [];

    res.json({
      success: true,
      data: transformedSessions,
      meta: {
        total: transformedSessions.length,
        timeRange,
        dateFilter
      }
    });

  } catch (error) {
    console.error('Error in study sessions route:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Create a new study session
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { 
      studySetId, 
      sessionType, 
      totalItems,
      sessionData = {} 
    } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
    }

    if (!studySetId || !sessionType || !totalItems) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: studySetId, sessionType, totalItems'
      });
    }

    // Verify the study set belongs to the user
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id, user_id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      return res.status(404).json({
        success: false,
        error: 'Study set not found or access denied'
      });
    }

    // Create the study session
    const { data: session, error } = await supabase
      .from('study_sessions')
      .insert({
        user_id: userId,
        study_set_id: studySetId,
        session_type: sessionType,
        total_items: totalItems,
        session_data: sessionData
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating study session:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to create study session'
      });
    }

    res.status(201).json({
      success: true,
      data: {
        id: session.id,
        studySetId: session.study_set_id,
        type: session.session_type,
        startTime: session.started_at,
        totalItems: session.total_items,
        completedItems: session.completed_items,
        sessionData: session.session_data
      }
    });

  } catch (error) {
    console.error('Error in create study session route:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Update a study session (typically to mark as completed)
router.put('/:sessionId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { sessionId } = req.params;
    const { 
      completedItems, 
      correctAnswers, 
      timeSpentSeconds,
      sessionData,
      endSession = false
    } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
    }

    // Prepare update data
    const updateData: any = {};
    
    if (completedItems !== undefined) updateData.completed_items = completedItems;
    if (correctAnswers !== undefined) updateData.correct_answers = correctAnswers;
    if (timeSpentSeconds !== undefined) updateData.time_spent_seconds = timeSpentSeconds;
    if (sessionData !== undefined) updateData.session_data = sessionData;
    if (endSession) updateData.ended_at = new Date().toISOString();

    // Update the study session
    const { data: session, error } = await supabase
      .from('study_sessions')
      .update(updateData)
      .eq('id', sessionId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating study session:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to update study session'
      });
    }

    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Study session not found or access denied'
      });
    }

    res.json({
      success: true,
      data: {
        id: session.id,
        studySetId: session.study_set_id,
        type: session.session_type,
        startTime: session.started_at,
        endTime: session.ended_at,
        totalItems: session.total_items,
        completedItems: session.completed_items,
        correctAnswers: session.correct_answers,
        timeSpent: session.time_spent_seconds,
        sessionData: session.session_data
      }
    });

  } catch (error) {
    console.error('Error in update study session route:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
