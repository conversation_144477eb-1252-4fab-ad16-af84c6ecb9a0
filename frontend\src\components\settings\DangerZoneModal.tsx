import React, { useState } from 'react';
import { HiX, HiExclamationCircle, HiTrash } from 'react-icons/hi';
import { Button } from '../common/Button';
import { Input } from '../common/Input';

interface DangerZoneModalProps {
  isOpen: boolean;
  onClose: () => void;
  action: 'deactivate' | 'delete';
}

export const DangerZoneModal: React.FC<DangerZoneModalProps> = ({
  isOpen,
  onClose,
  action
}) => {
  const [confirmationText, setConfirmationText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'confirm' | 'final'>('confirm');

  const actionConfig = {
    deactivate: {
      title: 'Deactivate Account',
      description: 'Your account will be temporarily disabled. You can reactivate it by logging in again.',
      confirmText: 'DEACTIVATE',
      buttonText: 'Deactivate Account',
      warningText: 'This will temporarily disable your account and log you out.',
      endpoint: '/api/auth/deactivate-account'
    },
    delete: {
      title: 'Delete Account',
      description: 'This will permanently delete your account and all associated data. This action cannot be undone.',
      confirmText: 'DELETE FOREVER',
      buttonText: 'Delete Account Forever',
      warningText: 'This will permanently delete all your data including study sets, flashcards, progress, and subscription information.',
      endpoint: '/api/auth/delete-account'
    }
  };

  const config = actionConfig[action];
  const isConfirmationValid = confirmationText === config.confirmText;

  const handleSubmit = async () => {
    if (step === 'confirm') {
      setStep('final');
      return;
    }

    if (!isConfirmationValid) {
      setError(`Please type "${config.confirmText}" to confirm`);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          confirmation: confirmationText
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to ${action} account`);
      }

      // For both actions, redirect to login page
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      window.location.href = '/login';

    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} account`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setConfirmationText('');
    setError(null);
    setStep('confirm');
    onClose();
  };

  const handleBack = () => {
    setStep('confirm');
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-red-500/30">
          <div className="flex items-center space-x-3">
            <HiExclamationCircle className="w-5 h-5 text-red-400" />
            <h3 className="text-lg font-semibold text-white">{config.title}</h3>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <HiX className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'confirm' ? (
            <div className="space-y-4">
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <HiExclamationCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-red-400 mb-2">Warning</h4>
                    <p className="text-gray-300 text-sm">{config.warningText}</p>
                  </div>
                </div>
              </div>

              <div>
                <p className="text-gray-300 text-sm mb-4">{config.description}</p>
                
                {action === 'delete' && (
                  <div className="space-y-2 text-sm text-gray-400">
                    <p>This will delete:</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>All study sets and flashcards</li>
                      <li>Quiz history and progress</li>
                      <li>Account settings and preferences</li>
                      <li>Subscription and billing information</li>
                      <li>All uploaded documents</li>
                    </ul>
                  </div>
                )}
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  onClick={handleClose}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={handleSubmit}
                  className="flex-1"
                >
                  Continue
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <HiTrash className="w-5 h-5 text-red-400" />
                  <h4 className="font-medium text-red-400">Final Confirmation</h4>
                </div>
              </div>

              <div>
                <p className="text-gray-300 text-sm mb-4">
                  To confirm this action, please type <span className="font-mono font-bold text-red-400">{config.confirmText}</span> in the box below:
                </p>
                
                <Input
                  type="text"
                  value={confirmationText}
                  onChange={(value) => {
                    setConfirmationText(value);
                    setError(null);
                  }}
                  placeholder={config.confirmText}
                  className="font-mono"
                  disabled={isLoading}
                />
              </div>

              {error && (
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                  <p className="text-red-400 text-sm">{error}</p>
                </div>
              )}

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  onClick={handleBack}
                  disabled={isLoading}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  variant="danger"
                  onClick={handleSubmit}
                  isLoading={isLoading}
                  disabled={isLoading || !isConfirmationValid}
                  className="flex-1"
                >
                  {isLoading ? 'Processing...' : config.buttonText}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
