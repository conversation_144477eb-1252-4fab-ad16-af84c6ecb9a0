import{r as m,j as e,R as re,c as _e,_ as ee,B as q,a as ne,b as se,H as ke,d as Se,e as Ce,u as Ee}from"./index-9c3838c3.js";import{useStudyStore as oe}from"./studyStore-0cac494e.js";import{u as Ae}from"./documentStore-fa7d7d9e.js";import{C as U,D as ce,a as de,d as ae,n as ue,b as me}from"./DifficultySelector-fe9acebe.js";import{u as he}from"./useUserSettings-3c6bd575.js";const xe=({value:r,onChange:d,label:i,min:u=1,max:n=100,placeholder:o="Enter a number",error:a,className:_="",disabled:b=!1})=>{const[v,N]=m.useState(r.toString()),[l,w]=m.useState("");m.useEffect(()=>{r.toString()!==v&&N(r.toString())},[r]);const k=j=>{if(N(j),w(""),j.trim()==="")return;if(!/^\d+$/.test(j.trim())){w("Please enter a whole number");return}const $=parseInt(j.trim(),10);if($<u){w(`Number must be at least ${u}`);return}if($>n){w(`Number must be at most ${n}`);return}d($)},Q=j=>{const $=j.target.value;($===""||/^\d+$/.test($))&&k($)},z=()=>{(v.trim()===""||l)&&(N(r.toString()),w(""))},T=j=>{!["Backspace","Delete","Tab","Escape","Enter","ArrowLeft","ArrowRight","ArrowUp","ArrowDown"].includes(j.key)&&!(j.key>="0"&&j.key<="9")&&!(j.ctrlKey&&["a","c","v","x","z"].includes(j.key.toLowerCase()))&&j.preventDefault()},D=()=>{const j=Math.min(r+1,n);d(j)},P=()=>{const j=Math.max(r-1,u);d(j)},S=a||l;return e.jsxs("div",{className:_,children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:i}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",inputMode:"numeric",value:v,onChange:Q,onBlur:z,onKeyDown:T,placeholder:o,disabled:b,className:`
            w-full px-3 py-2 pr-20 bg-background-primary border rounded-lg text-white 
            placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors
            ${S?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"border-gray-600 focus:border-primary-500 focus:ring-primary-500/50"}
            ${b?"opacity-50 cursor-not-allowed":""}
          `}),e.jsxs("div",{className:"absolute right-1 top-1 bottom-1 flex flex-col",children:[e.jsx("button",{type:"button",onClick:D,disabled:b||r>=n,className:`\r
              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r
              rounded-tr-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r
              focus:outline-none focus:bg-gray-600\r
            `,tabIndex:-1,children:e.jsx("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})})}),e.jsx("button",{type:"button",onClick:P,disabled:b||r<=u,className:`\r
              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r
              rounded-br-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r
              focus:outline-none focus:bg-gray-600\r
            `,tabIndex:-1,children:e.jsx("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]}),S&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:S}),!S&&e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["Enter a number between ",u," and ",n]})]})},$e=({progress:r=0,isIndeterminate:d=!1,label:i,className:u="",size:n="md",variant:o="primary"})=>{const a={sm:"h-1.5",md:"h-2",lg:"h-3"},_={primary:"bg-primary-500",success:"bg-green-500",warning:"bg-yellow-500",error:"bg-red-500"},b={sm:"text-xs",md:"text-sm",lg:"text-base"};return e.jsxs("div",{className:`w-full ${u}`,children:[i&&e.jsxs("div",{className:`flex justify-between items-center mb-2 ${b[n]}`,children:[e.jsx("span",{className:"text-gray-300 font-medium",children:i}),!d&&e.jsxs("span",{className:"text-gray-400",children:[Math.round(r),"%"]})]}),e.jsx("div",{className:`w-full bg-gray-700 rounded-full overflow-hidden ${a[n]}`,children:e.jsx("div",{className:`${_[o]} transition-all duration-300 ease-out rounded-full ${a[n]} ${d?"animate-pulse w-full":"transition-[width] duration-500"}`,style:d?void 0:{width:`${Math.min(100,Math.max(0,r))}%`}})})]})},ge=({isGenerating:r,stage:d,estimatedTime:i,className:u=""})=>{const[n,o]=re.useState(0),[a,_]=re.useState(0);if(re.useEffect(()=>{let v,N;return r?(_(0),o(0),v=setInterval(()=>{_(l=>l+1)},1e3),N=setInterval(()=>{o(l=>{const w=l<30?3:l<60?2:l<85?1:.2;return Math.min(90,l+w)})},1e3)):(o(0),_(0)),()=>{v&&clearInterval(v),N&&clearInterval(N)}},[r]),re.useEffect(()=>{!r&&n>0&&(o(100),setTimeout(()=>o(0),1e3))},[r,n]),!r&&n===0)return null;const b=v=>{if(v<60)return`${v}s`;const N=Math.floor(v/60),l=v%60;return`${N}m ${l}s`};return e.jsx("div",{className:`bg-background-secondary rounded-lg p-4 border border-gray-600 ${u}`,children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-primary-500 border-t-transparent"}),e.jsx("span",{className:"text-white font-medium",children:d||"Generating with AI..."})]}),e.jsxs("div",{className:"text-sm text-gray-400",children:[a>0&&b(a),i&&a===0&&` (Est. ${b(i)})`]})]}),e.jsx($e,{progress:n,isIndeterminate:n===0,variant:"primary",size:"md"}),e.jsx("div",{className:"text-xs text-gray-500 text-center",children:r?"Please wait while we generate your content...":"Generation complete!"})]})})},ye=({selectedDocuments:r,onSelectionChange:d,maxSelection:i=5})=>{const{documents:u,fetchDocuments:n,isLoading:o}=Ae(),[a,_]=m.useState("");m.useEffect(()=>{u.length===0&&n()},[u.length,n]);const b=u.filter(l=>l.is_processed&&l.filename.toLowerCase().includes(a.toLowerCase())),v=l=>{r.includes(l)?d(r.filter(k=>k!==l)):r.length<i&&d([...r,l])},N=()=>u.filter(l=>r.includes(l.id));return o?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):u.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No documents found"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx("input",{type:"text",placeholder:"Search documents...",value:a,onChange:l=>_(l.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),r.length>0&&e.jsxs("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[e.jsxs("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",r.length," of ",i," documents:"]}),e.jsx("div",{className:"space-y-1",children:N().map(l=>e.jsxs("div",{className:"text-sm text-gray-300 flex items-center justify-between",children:[e.jsx("span",{className:"truncate",children:l.filename}),e.jsx("button",{onClick:()=>v(l.id),className:"text-red-400 hover:text-red-300 ml-2",children:"✕"})]},l.id))})]}),e.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:b.map(l=>{const w=r.includes(l.id),k=!w&&r.length<i;return e.jsx("div",{className:`
                p-3 rounded-lg border cursor-pointer transition-all
                ${w?"bg-primary-500/20 border-primary-500":k?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"}
              `,onClick:()=>k||w?v(l.id):null,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg",children:l.file_type==="pdf"?"📄":l.file_type==="docx"?"📝":l.file_type==="txt"?"📃":"📊"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-white font-medium truncate",children:l.filename}),e.jsxs("p",{className:"text-sm text-gray-400",children:[l.file_type.toUpperCase()," • ",Math.round(l.file_size/1024)," KB"]})]})]})}),e.jsx("div",{className:`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${w?"bg-primary-500 border-primary-500":"border-gray-500"}
                `,children:w&&e.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},l.id)})}),b.length===0&&a&&e.jsx("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})},Fe=[{value:U.SHORT,label:"Short",description:"Concise answers (1-2 sentences)",icon:"📝"},{value:U.MEDIUM,label:"Medium",description:"Balanced detail (2-3 sentences)",icon:"📄"},{value:U.LONG,label:"Long",description:"Comprehensive answers (3-5 sentences)",icon:"📋"}],qe=r=>{switch(r){case U.SHORT:return"bg-background-secondary text-text-primary border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50";case U.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case U.LONG:return"bg-background-secondary text-text-primary border-indigo-500/30 hover:bg-indigo-500/10 hover:border-indigo-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}},Te=r=>{switch(r){case U.SHORT:return"bg-emerald-500/20 text-emerald-300 border-emerald-500 shadow-lg shadow-emerald-500/20";case U.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case U.LONG:return"bg-indigo-500/20 text-indigo-300 border-indigo-500 shadow-lg shadow-indigo-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}},pe=({value:r,onChange:d,className:i="",disabled:u=!1,label:n="Content Length"})=>e.jsxs("div",{className:`space-y-3 ${i}`,children:[e.jsx("label",{className:"block text-sm font-medium text-text-primary",children:n}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:Fe.map(o=>{const a=r===o.value,_=a?Te(o.value):qe(o.value);return e.jsxs("button",{type:"button",onClick:()=>!u&&d(o.value),disabled:u,className:`
                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${_}
                ${u?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105"}
                ${a?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `,title:o.description,"aria-pressed":a,children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("div",{className:"text-2xl",children:o.icon}),e.jsx("div",{className:"font-semibold",children:o.label}),e.jsx("div",{className:`text-xs ${a?"text-white/90":"text-text-secondary"}`,children:o.description})]}),a&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},o.value)})}),e.jsx("p",{className:"text-xs text-text-muted",children:"Choose how detailed you want the flashcard answers to be. This affects the length and depth of explanations."})]}),De=_e(r=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async d=>{r({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");r({generationProgress:"Generating flashcards with AI..."});const u=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(d)});if(!u.ok){const o=await u.json();throw new Error(o.error||"Generation failed")}const n=await u.json();if(n.success){r({lastGenerated:{studySet:n.data.studySet,content:n.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""});try{const{useStudyStore:o}=await ee(()=>import("./studyStore-0cac494e.js"),["assets/studyStore-0cac494e.js","assets/index-9c3838c3.js","assets/index-547d5289.css"]),a=o.getState();a.refreshStudySetContent(n.data.studySet.id),a.invalidateStudySets()}catch(o){console.warn("Failed to refresh study set cache:",o)}return{studySet:n.data.studySet,flashcards:n.data.flashcards,creditsRemaining:n.data.creditsRemaining}}else throw new Error(n.error)}catch(i){throw r({isGenerating:!1,generationProgress:""}),i}},generateQuiz:async d=>{r({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");r({generationProgress:"Generating quiz questions with AI..."});const u=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(d)});if(!u.ok){const o=await u.json();throw new Error(o.error||"Generation failed")}const n=await u.json();if(n.success){r({lastGenerated:{studySet:n.data.studySet,content:n.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""});try{const{useStudyStore:o}=await ee(()=>import("./studyStore-0cac494e.js"),["assets/studyStore-0cac494e.js","assets/index-9c3838c3.js","assets/index-547d5289.css"]),a=o.getState();a.refreshStudySetContent(n.data.studySet.id),a.invalidateStudySets()}catch(o){console.warn("Failed to refresh study set cache:",o)}return{studySet:n.data.studySet,questions:n.data.questions,creditsRemaining:n.data.creditsRemaining}}else throw new Error(n.error)}catch(i){throw r({isGenerating:!1,generationProgress:""}),i}},generateMoreFlashcards:async d=>{r({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");r({generationProgress:"Generating additional flashcards with AI..."});const u=await fetch("/api/ai/generate-more-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(d)});if(!u.ok){const o=await u.json();throw new Error(o.error||"Generation failed")}const n=await u.json();if(n.success){r({isGenerating:!1,generationProgress:""});try{const{useStudyStore:o}=await ee(()=>import("./studyStore-0cac494e.js"),["assets/studyStore-0cac494e.js","assets/index-9c3838c3.js","assets/index-547d5289.css"]);o.getState().refreshStudySetContent(d.studySetId)}catch(o){console.warn("Failed to refresh study set cache:",o)}return{flashcards:n.data.flashcards,creditsRemaining:n.data.creditsRemaining}}else throw new Error(n.error)}catch(i){throw r({isGenerating:!1,generationProgress:""}),i}},clearLastGenerated:()=>{r({lastGenerated:null})}})),Ie=({selectedCount:r,totalCount:d,onDeleteSelected:i,onClearSelection:u,isLoading:n=!1,className:o=""})=>r===0?null:e.jsx("div",{className:`bg-gray-800 border border-gray-700 rounded-lg p-4 mb-4 ${o}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:[r," of ",d," flashcard",r!==1?"s":""," selected"]}),e.jsx("button",{onClick:u,className:"text-gray-400 hover:text-white text-sm underline",disabled:n,children:"Clear selection"})]}),e.jsx("div",{className:"flex items-center space-x-3",children:e.jsx(q,{onClick:i,variant:"danger",size:"sm",isLoading:n,disabled:n,className:"px-4 py-2",children:"Delete Selected"})})]})}),Me=({studySetId:r,flashcards:d,onFlashcardAdded:i,onFlashcardUpdated:u,onFlashcardDeleted:n,onFlashcardsGenerated:o})=>{const{alert:a,confirm:_}=ne(),{user:b}=se(),{generateMoreFlashcards:v}=De(),{settings:N,updateSettings:l}=he(),[w,k]=m.useState(!0),[Q,z]=m.useState([]),[T,D]=m.useState(20),[P,S]=m.useState(""),[j,$]=m.useState(ce.MEDIUM),[K,W]=m.useState(U.MEDIUM),[B,g]=m.useState(!1),[I,J]=m.useState(!1),[M,h]=m.useState({front:"",back:"",difficulty_level:3}),[E,X]=m.useState(null),[O,H]=m.useState({front:"",back:"",difficulty_level:3}),[A,R]=m.useState([]),[Y,x]=m.useState(!1),[f,G]=m.useState(!1),[Z,t]=m.useState(!1),c=(s,p)=>{p?R(F=>[...F,s]):(R(F=>F.filter(L=>L!==s)),x(!1))},y=s=>{x(s),R(s?d.map(p=>p.id):[])},C=()=>{R([]),x(!1)},V=async()=>{if(A.length!==0){if(!(N!=null&&N.skip_delete_confirmations)){let s=!1;if(!await _({title:"Delete Flashcards",message:`Are you sure you want to delete ${A.length} flashcard${A.length!==1?"s":""}?`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:F=>{s=F}}))return;if(s)try{await l({skip_delete_confirmations:!0})}catch(F){console.error("Failed to update user settings:",F)}}await te()}},te=async()=>{G(!0);try{const s=await fetch("/api/flashcards/bulk-delete",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`,"Content-Type":"application/json"},body:JSON.stringify({flashcardIds:A})});if(!s.ok){const L=await s.json();throw new Error(L.error||"Failed to delete flashcards")}const p=await s.json(),{deletedCount:F}=p.data;A.forEach(L=>n(L)),C(),await a({title:"Success",message:`${F} flashcard${F!==1?"s":""} deleted successfully!`,variant:"success"})}catch(s){await a({title:"Error",message:s.message||"Failed to delete flashcards",variant:"error"})}finally{G(!1)}},fe=async()=>{if(!M.front.trim()||!M.back.trim()){await a({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const s=await fetch(`/api/flashcards/study-set/${r}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:M.front.trim(),back:M.back.trim(),difficulty_level:M.difficulty_level,is_ai_generated:!1})});if(!s.ok)throw new Error("Failed to create flashcard");const p=await s.json();i(p.data),h({front:"",back:"",difficulty_level:3}),J(!1),await a({title:"Success",message:"Flashcard added successfully!",variant:"success"})}catch(s){await a({title:"Error",message:s.message||"Failed to add flashcard",variant:"error"})}},be=s=>{X(s),H({front:s.front,back:s.back,difficulty_level:typeof s.difficulty_level=="string"?me(s.difficulty_level):s.difficulty_level||3})},ve=async()=>{if(E){if(!O.front.trim()||!O.back.trim()){await a({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const s=await fetch(`/api/flashcards/${E.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:O.front.trim(),back:O.back.trim(),difficulty_level:O.difficulty_level})});if(!s.ok)throw new Error("Failed to update flashcard");const p=await s.json();u(p.data),X(null),H({front:"",back:"",difficulty_level:3}),await a({title:"Success",message:"Flashcard updated successfully!",variant:"success"})}catch(s){await a({title:"Error",message:s.message||"Failed to update flashcard",variant:"error"})}}},je=()=>{X(null),H({front:"",back:"",difficulty_level:3})},we=async s=>{if(N!=null&&N.skip_delete_confirmations){await ie(s);return}let p=!1;if(await _({title:"Delete Flashcard",message:`Are you sure you want to delete this flashcard?

Front: ${s.front.substring(0,50)}${s.front.length>50?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:L=>{p=L}})){if(p)try{await l({skip_delete_confirmations:!0})}catch(L){console.error("Failed to update user settings:",L)}await ie(s)}},ie=async s=>{try{if(!(await fetch(`/api/flashcards/${s.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete flashcard");n(s.id),await a({title:"Success",message:"Flashcard deleted successfully!",variant:"success"})}catch(p){await a({title:"Error",message:p.message||"Failed to delete flashcard",variant:"error"})}},le=()=>Math.ceil(T/5),Ne=async()=>{if(Q.length===0){await a({title:"No Documents Selected",message:"Please select at least one document to generate flashcards from.",variant:"warning"});return}const s=le();if(b&&b.credits_remaining<s){await a({title:"Insufficient Credits",message:`You need ${s} credits to generate ${T} flashcards, but you only have ${b.credits_remaining} credits remaining.`,variant:"error"});return}if(await _({title:"Generate Flashcards",message:`Generate ${T} flashcards from ${Q.length} document(s)?

This will cost ${s} credits.`,confirmText:"Generate",cancelText:"Cancel"})){g(!0);try{const F=await v({studySetId:r,documentIds:Q,count:T,customPrompt:P.trim()||void 0,difficultyLevel:j,contentLength:K});o(F.flashcards),b&&se.getState().updateUser({credits_remaining:F.creditsRemaining});try{const{useStudyStore:L}=await ee(()=>import("./studyStore-0cac494e.js"),["assets/studyStore-0cac494e.js","assets/index-9c3838c3.js","assets/index-547d5289.css"]);await L.getState().refreshStudySetContent(r)}catch(L){console.warn("Failed to refresh study set cache:",L)}await a({title:"Success",message:`Generated ${F.flashcards.length} flashcards successfully!`,variant:"success"}),z([]),S(""),k(!1)}catch(F){await a({title:"Generation Error",message:F.message||"Failed to generate flashcards",variant:"error"})}finally{g(!1)}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Manage Flashcards"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(q,{onClick:()=>J(!I),variant:"secondary",size:"sm",children:"➕ Add Flashcard"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"AI Mode"}),e.jsx("button",{onClick:()=>k(!w),className:`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${w?"bg-primary-500":"bg-gray-600"}
              `,children:e.jsx("span",{className:`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${w?"translate-x-6":"translate-x-1"}
                `})})]})]})]}),I&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:M.front,onChange:s=>h(p=>({...p,front:s.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:M.back,onChange:s=>h(p=>({...p,back:s.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:M.difficulty_level,onChange:s=>h(p=>({...p,difficulty_level:parseInt(s.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(q,{onClick:fe,variant:"primary",children:"Add Flashcard"}),e.jsx(q,{onClick:()=>J(!1),variant:"secondary",children:"Cancel"})]})]})]}),E&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:O.front,onChange:s=>H(p=>({...p,front:s.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:O.back,onChange:s=>H(p=>({...p,back:s.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:O.difficulty_level,onChange:s=>H(p=>({...p,difficulty_level:parseInt(s.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(q,{onClick:ve,variant:"primary",children:"Save Changes"}),e.jsx(q,{onClick:je,variant:"secondary",children:"Cancel"})]})]})]}),w&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Flashcard Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(ye,{selectedDocuments:Q,onSelectionChange:z,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(xe,{label:"Number of Flashcards",value:T,onChange:D,min:1,max:100,placeholder:"Enter number (1-100)",disabled:B}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[le()," credits"]})]})]}),e.jsx(de,{value:j,onChange:$}),e.jsx(pe,{value:K,onChange:W}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:P,onChange:s=>S(s.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for flashcard generation..."})]}),e.jsx(q,{onClick:Ne,disabled:Q.length===0||B,className:"w-full",variant:"primary",children:B?"Generating...":`Generate ${T} Flashcards`})]})]}),e.jsx(ge,{isGenerating:B,stage:B?"Generating flashcards with AI...":void 0,estimatedTime:Math.ceil(T/10)}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Flashcards (",d.length,")"]}),d.length>0&&e.jsx("div",{className:"flex items-center space-x-3",children:e.jsxs("label",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[e.jsx("input",{type:"checkbox",checked:Y,onChange:s=>y(s.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"}),e.jsx("span",{children:"Select All"})]})})]}),e.jsx(Ie,{selectedCount:A.length,totalCount:d.length,onDeleteSelected:V,onClearSelection:C,isLoading:f}),d.length>0&&e.jsx("div",{className:"flex justify-end mb-4",children:e.jsx("button",{onClick:()=>t(!Z),className:"flex items-center space-x-2 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-lg text-gray-300 hover:text-white hover:border-primary-500 transition-colors",children:Z?e.jsxs(e.Fragment,{children:[e.jsx(ke,{className:"w-4 h-4"}),e.jsx("span",{children:"Hide Back Content"})]}):e.jsxs(e.Fragment,{children:[e.jsx(Se,{className:"w-4 h-4"}),e.jsx("span",{children:"Show Back Content"})]})})}),d.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No flashcards yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:d.map(s=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 pt-1",children:e.jsx("input",{type:"checkbox",checked:A.includes(s.id),onChange:p=>c(s.id,p.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Front"}),e.jsx("p",{className:"text-white font-medium",children:s.front})]}),Z&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Back"}),e.jsx("p",{className:"text-gray-300",children:s.back})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[s.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),s.difficulty_level&&e.jsxs("span",{children:["Difficulty:"," ",typeof s.difficulty_level=="string"?ae(s.difficulty_level):ae(ue(s.difficulty_level))]}),e.jsxs("span",{children:["Reviewed: ",s.times_reviewed||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>be(s),className:"text-gray-400 hover:text-white p-1",title:"Edit flashcard",children:"✏️"}),e.jsx("button",{onClick:()=>we(s),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete flashcard",children:"🗑️"})]})]})})]})},s.id))})]})]})},Ge=({studySetId:r,questions:d,onQuestionAdded:i,onQuestionUpdated:u,onQuestionDeleted:n,onQuestionsGenerated:o})=>{const{alert:a,confirm:_}=ne(),{user:b}=se(),[v,N]=m.useState(!0),[l,w]=m.useState([]),[k,Q]=m.useState(20),[z,T]=m.useState(""),[D,P]=m.useState(ce.MEDIUM),[S,j]=m.useState(U.MEDIUM),[$,K]=m.useState(!1),[W,B]=m.useState(!1),[g,I]=m.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[J,M]=m.useState(null),[h,E]=m.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),X=async()=>{if(!g.question_text.trim()){await a({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(g.correct_answers.length===0){await a({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((g.question_type==="multiple_choice"||g.question_type==="select_all")&&g.options.filter(c=>c.trim().length>0).length<2){await a({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/study-set/${r}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:g.question_text.trim(),question_type:g.question_type,options:g.question_type==="multiple_choice"||g.question_type==="select_all"?g.options.filter(y=>y.trim().length>0):null,correct_answers:g.correct_answers,explanation:g.explanation.trim()||null,difficulty_level:g.difficulty_level})});if(!t.ok)throw new Error("Failed to create question");const c=await t.json();i(c.data),I({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),B(!1),await a({title:"Success",message:"Question added successfully!",variant:"success"})}catch(t){await a({title:"Error",message:t.message||"Failed to add question",variant:"error"})}},O=async t=>{if(await _({title:"Delete Question",message:`Are you sure you want to delete this question?

${t.question_text.substring(0,100)}${t.question_text.length>100?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/quiz-questions/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete question");n(t.id),await a({title:"Success",message:"Question deleted successfully!",variant:"success"})}catch(y){await a({title:"Error",message:y.message||"Failed to delete question",variant:"error"})}},H=t=>{M(t),E({question_text:t.question_text,question_type:t.question_type,options:t.options||["","","",""],correct_answers:t.correct_answers,explanation:t.explanation||"",difficulty_level:typeof t.difficulty_level=="string"?me(t.difficulty_level):t.difficulty_level||3})},A=async()=>{if(J){if(!h.question_text.trim()){await a({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(h.correct_answers.length===0){await a({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((h.question_type==="multiple_choice"||h.question_type==="select_all")&&h.options.filter(c=>c.trim().length>0).length<2){await a({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/${J.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:h.question_text.trim(),question_type:h.question_type,options:h.question_type==="multiple_choice"||h.question_type==="select_all"?h.options.filter(y=>y.trim().length>0):null,correct_answers:h.correct_answers,explanation:h.explanation.trim()||null,difficulty_level:h.difficulty_level})});if(!t.ok)throw new Error("Failed to update question");const c=await t.json();u(c.data),M(null),await a({title:"Success",message:"Question updated successfully!",variant:"success"})}catch(t){await a({title:"Error",message:t.message||"Failed to update question",variant:"error"})}}},R=()=>{M(null),E({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3})},Y=()=>Math.ceil(k/5),x=async()=>{if(l.length===0){await a({title:"No Documents Selected",message:"Please select at least one document to generate questions from.",variant:"warning"});return}const t=Y();if(b&&b.credits_remaining<t){await a({title:"Insufficient Credits",message:`You need ${t} credits to generate ${k} questions, but you only have ${b.credits_remaining} credits remaining.`,variant:"warning"});return}if(await _({title:"Generate Questions",message:`Generate ${k} questions for ${t} credits?`,confirmText:"Generate",cancelText:"Cancel"})){K(!0);try{const y=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({studySetId:r,documentIds:l,count:k,customPrompt:z.trim()||void 0,difficultyLevel:D,contentLength:S})});if(!y.ok)throw new Error("Failed to generate questions");const C=await y.json();o(C.data.questions),b&&se.getState().updateUser({credits_remaining:C.data.creditsRemaining});try{const{useStudyStore:V}=await ee(()=>import("./studyStore-0cac494e.js"),["assets/studyStore-0cac494e.js","assets/index-9c3838c3.js","assets/index-547d5289.css"]);await V.getState().refreshStudySetContent(r)}catch(V){console.warn("Failed to refresh study set cache:",V)}await a({title:"Success",message:`Generated ${C.data.questions.length} questions successfully!`,variant:"success"}),w([]),T(""),N(!1)}catch(y){await a({title:"Error",message:y.message||"Failed to generate questions",variant:"error"})}finally{K(!1)}}},f=t=>{I(c=>({...c,question_type:t,options:t==="multiple_choice"||t==="select_all"?["","","",""]:[],correct_answers:[]}))},G=(t,c)=>{I(y=>({...y,options:y.options.map((C,V)=>V===t?c:C)}))},Z=t=>{I(c=>{const y=c.correct_answers.includes(t);return c.question_type==="multiple_choice"?{...c,correct_answers:y?[]:[t]}:{...c,correct_answers:y?c.correct_answers.filter(C=>C!==t):[...c.correct_answers,t]}})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Question Management"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(q,{onClick:()=>B(!W),variant:"secondary",size:"sm",children:W?"Cancel":"Add Question"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"AI Mode"}),e.jsx("button",{onClick:()=>N(!v),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${v?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${v?"translate-x-6":"translate-x-1"}`})})]})]})]}),W&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:g.question_text,onChange:t=>I(c=>({...c,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:g.question_type,onChange:t=>f(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(g.question_type==="multiple_choice"||g.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:g.options.map((t,c)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>Z(t),className:`flex-shrink-0 w-5 h-5 border-2 ${g.question_type==="multiple_choice"?"rounded-full":"rounded"} ${g.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:g.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:y=>G(c,y.target.value),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${c+1}`})]},c))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:g.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),g.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>I(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${g.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>I(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${g.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),g.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:g.correct_answers.join(`
`),onChange:t=>I(c=>({...c,correct_answers:t.target.value.split(`
`).filter(y=>y.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:g.explanation,onChange:t=>I(c=>({...c,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:g.difficulty_level,onChange:t=>I(c=>({...c,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(q,{onClick:X,variant:"primary",children:"Add Question"}),e.jsx(q,{onClick:()=>B(!1),variant:"secondary",children:"Cancel"})]})]})]}),J&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:h.question_text,onChange:t=>E(c=>({...c,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:h.question_type,onChange:t=>E(c=>({...c,question_type:t.target.value,options:t.target.value==="multiple_choice"||t.target.value==="select_all"?["","","",""]:[],correct_answers:[]})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(h.question_type==="multiple_choice"||h.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:h.options.map((t,c)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>{const y=h.correct_answers.includes(t);h.question_type==="multiple_choice"?E(C=>({...C,correct_answers:y?[]:[t]})):E(C=>({...C,correct_answers:y?C.correct_answers.filter(V=>V!==t):[...C.correct_answers,t]}))},className:`flex-shrink-0 w-5 h-5 border-2 ${h.question_type==="multiple_choice"?"rounded-full":"rounded"} ${h.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:h.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:y=>E(C=>({...C,options:C.options.map((V,te)=>te===c?y.target.value:V)})),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${c+1}`})]},c))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:h.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),h.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>E(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${h.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>E(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${h.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),h.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:h.correct_answers.join(`
`),onChange:t=>E(c=>({...c,correct_answers:t.target.value.split(`
`).filter(y=>y.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:h.explanation,onChange:t=>E(c=>({...c,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:h.difficulty_level,onChange:t=>E(c=>({...c,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(q,{onClick:A,variant:"primary",children:"Save Changes"}),e.jsx(q,{onClick:R,variant:"secondary",children:"Cancel"})]})]})]}),v&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Question Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(ye,{selectedDocuments:l,onSelectionChange:w,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(xe,{label:"Number of Questions",value:k,onChange:Q,min:1,max:100,placeholder:"Enter number (1-100)",disabled:$}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[Y()," credits"]})]})]}),e.jsx(de,{value:D,onChange:P}),e.jsx(pe,{value:S,onChange:j}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:z,onChange:t=>T(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for question generation..."})]}),e.jsx(q,{onClick:x,disabled:l.length===0||$,className:"w-full",variant:"primary",children:$?"Generating...":`Generate ${k} Questions`})]})]}),e.jsx(ge,{isGenerating:$,stage:$?"Generating quiz questions with AI...":void 0,estimatedTime:Math.ceil(k/8)}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Questions (",d.length,")"]}),d.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No questions yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:d.map(t=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Question"}),e.jsx("p",{className:"text-white font-medium",children:t.question_text})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Type"}),e.jsx("p",{className:"text-gray-300 capitalize",children:t.question_type.replace("_"," ")})]}),t.options&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Options"}),e.jsx("ul",{className:"text-gray-300 text-sm",children:t.options.map((c,y)=>e.jsxs("li",{className:`${t.correct_answers.includes(c)?"text-green-400 font-medium":""}`,children:[y+1,". ",c]},`${t.id}-option-${y}`))})]}),t.explanation&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Explanation"}),e.jsx("p",{className:"text-gray-300 text-sm",children:t.explanation})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[t.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),t.difficulty_level&&e.jsxs("span",{children:["Difficulty:"," ",typeof t.difficulty_level=="string"?ae(t.difficulty_level):ae(ue(t.difficulty_level))]}),e.jsxs("span",{children:["Attempted: ",t.times_attempted||0," times"]}),e.jsxs("span",{children:["Correct: ",t.times_correct||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>H(t),className:"text-gray-400 hover:text-primary-400 p-1",title:"Edit question",children:"✏️"}),e.jsx("button",{onClick:()=>O(t),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete question",children:"🗑️"})]})]})},t.id))})]})]})},Le=({enabled:r,onChange:d,disabled:i=!1,className:u="",label:n="Shuffle Cards",description:o="Randomize the order of flashcards during study sessions"})=>{const a=()=>{i||d(!r)},_=b=>{(b.key===" "||b.key==="Enter")&&(b.preventDefault(),a())};return e.jsxs("div",{className:`flex items-center justify-between ${u}`,children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:n}),o&&e.jsx("span",{className:"text-xs text-gray-400",children:o})]})}),e.jsx("button",{type:"button",role:"switch","aria-checked":r,"aria-label":`${r?"Disable":"Enable"} ${n.toLowerCase()}`,onClick:a,onKeyDown:_,disabled:i,className:`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800
          ${i?"opacity-50 cursor-not-allowed bg-gray-600":r?"bg-primary-500 hover:bg-primary-600":"bg-gray-600 hover:bg-gray-500"}
        `,children:e.jsx("span",{className:`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out
            ${r?"translate-x-6":"translate-x-1"}
          `})})]})},Ve=()=>{const{id:r}=Ce(),d=Ee(),{studySetContent:i,isLoading:u,error:n,fetchStudySetContent:o}=oe(),{alert:a,confirm:_,prompt:b}=ne(),{settings:v,updateSettings:N}=he(),[l,w]=m.useState(null),[k,Q]=m.useState("study"),[z,T]=m.useState("flashcards"),[D,P]=m.useState([]),[S,j]=m.useState([]),[$,K]=m.useState("");m.useEffect(()=>{r&&o(r).catch(console.error)},[r,o]),m.useEffect(()=>{i!=null&&i.studySet&&(K(i.studySet.name),P(i.flashcards||[]),j(i.questions||[]))},[i]);const W=async()=>{if(!(!r||!l))try{const x=(v==null?void 0:v.shuffle_flashcards)||!1;await oe.getState().startStudySession(r,l,x),d(`/study/${r}/${l}`)}catch(x){await a({title:"Error",message:x.message||"Failed to start study session",variant:"error"})}},B=async()=>{if(!r||!(i!=null&&i.studySet))return;const x=await b({title:"Rename Study Set",message:"Enter a new name for this study set:",defaultValue:i.studySet.name});if(!(x===null||x.trim()===i.studySet.name)){if(!x.trim()){await a({title:"Invalid Name",message:"Study set name cannot be empty.",variant:"error"});return}try{if(!(await fetch(`/api/study-sets/${r}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({name:x.trim()})})).ok)throw new Error("Failed to rename study set");K(x.trim()),await a({title:"Success",message:"Study set renamed successfully!",variant:"success"}),await o(r)}catch(f){await a({title:"Error",message:f.message||"Failed to rename study set",variant:"error"})}}},g=async()=>{if(!(!r||!(i!=null&&i.studySet)||!await _({title:"Delete Study Set",message:`Are you sure you want to delete "${i.studySet.name}"?

This action cannot be undone and will delete all flashcards and quiz questions in this set.`,variant:"danger",confirmText:"Delete Study Set",cancelText:"Cancel"})))try{if(!(await fetch(`/api/study-sets/${r}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete study set");await a({title:"Success",message:"Study set deleted successfully!",variant:"success"}),d("/dashboard")}catch(f){await a({title:"Error",message:f.message||"Failed to delete study set",variant:"error"})}},I=x=>{P(f=>[...f,x])},J=x=>{P(f=>f.map(G=>G.id===x.id?x:G))},M=x=>{P(f=>f.filter(G=>G.id!==x))},h=x=>{P(f=>[...f,...x])},E=x=>{j(f=>[...f,x])},X=x=>{j(f=>f.map(G=>G.id===x.id?x:G))},O=x=>{j(f=>f.filter(G=>G.id!==x))},H=x=>{j(f=>[...f,...x])};if(u)return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(n||!(i!=null&&i.studySet))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-red-400 mb-4",children:n||"Study set not found"}),e.jsx(q,{onClick:()=>d("/dashboard"),variant:"secondary",children:"Back to Study Sets"})]})});const{studySet:A}=i,R=D&&D.length>0,Y=S&&S.length>0;return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("button",{onClick:()=>d("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"← Back to Study Sets"}),e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:$}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(q,{onClick:B,variant:"secondary",size:"sm",children:"✏️ Rename"}),e.jsx(q,{onClick:g,variant:"danger",size:"sm",children:"🗑️ Delete"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[e.jsx("span",{className:"capitalize",children:A.type}),A.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.jsxs("span",{children:["Created ",new Date(A.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-600",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{onClick:()=>Q("study"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${k==="study"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"📚 Study Mode"}),e.jsx("button",{onClick:()=>Q("manage"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${k==="manage"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"⚙️ Manage Content"})]})})}),k==="study"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[R&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${l==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>w("flashcards"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"🃏"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Flashcard Review"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[D==null?void 0:D.length," flashcards • Interactive review"]})]})]})}),Y&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${l==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>w("quiz"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"📝"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Quiz Practice"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[S==null?void 0:S.length," questions • Test your knowledge"]})]})]})})]}),l&&v&&e.jsx("div",{className:"mb-6 p-4 bg-background-tertiary rounded-lg border border-gray-600",children:e.jsx(Le,{enabled:v.shuffle_flashcards,onChange:async x=>{try{await N({shuffle_flashcards:x})}catch(f){console.error("Failed to update shuffle setting:",f)}},label:"Shuffle Cards",description:"Randomize the order of flashcards during study sessions"})}),e.jsx(q,{onClick:W,disabled:!l,className:"w-full",size:"lg",children:l?`Start ${l==="flashcards"?"Flashcard Review":"Quiz Practice"}`:"Select a study mode"})]})}),k==="manage"&&r&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex space-x-1 mb-6",children:[e.jsxs("button",{onClick:()=>T("flashcards"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${z==="flashcards"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["📚 Flashcards (",D.length,")"]}),e.jsxs("button",{onClick:()=>T("quiz"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${z==="quiz"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["❓ Quiz Questions (",S.length,")"]})]}),z==="flashcards"&&e.jsx(Me,{studySetId:r,flashcards:D,onFlashcardAdded:I,onFlashcardUpdated:J,onFlashcardDeleted:M,onFlashcardsGenerated:h}),z==="quiz"&&e.jsx(Ge,{studySetId:r,questions:S,onQuestionAdded:E,onQuestionUpdated:X,onQuestionDeleted:O,onQuestionsGenerated:H})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-400",children:[R&&e.jsxs("div",{children:[D.length," flashcards"]}),Y&&e.jsxs("div",{children:[S==null?void 0:S.length," quiz questions"]}),!R&&!Y&&e.jsx("div",{className:"text-gray-500",children:"No content yet"})]})]}),A.source_documents&&A.source_documents.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),e.jsx("div",{className:"space-y-1 text-sm text-gray-400",children:A.source_documents.map((x,f)=>e.jsx("div",{children:x.filename},f))})]}),A.custom_prompt&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),e.jsx("p",{className:"text-sm text-gray-400",children:A.custom_prompt})]})]})]})]})};export{Ve as StudySetPage};
