import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  HiCreditCard,
  HiSparkles,
  HiCheck,
  HiStar,
  HiLightningBolt
} from 'react-icons/hi';
import { Button } from '../common/Button';
import { useCreditStore } from '../../stores/creditStore';
import { useAuthStore } from '../../stores/authStore';

interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  bonus?: number;
  popular?: boolean;
  description: string;
  features: string[];
}

interface CreditPurchaseProps {
  currentBalance: number;
  userTier: string;
  onPurchaseComplete: () => void;
}

const creditPackages: CreditPackage[] = [
  {
    id: 'study-buddy',
    name: 'Study Buddy',
    credits: 100,
    price: 9.99,
    description: 'Perfect for high school & early college students',
    features: [
      '100 study credits (500 flashcards/quizzes)',
      'Covers 2-3 classes per semester',
      'Valid for 6 months',
      'All AI study tools included',
      'Perfect for finals prep'
    ]
  },
  {
    id: 'deans-list',
    name: "Dean's List",
    credits: 500,
    price: 39.99,
    bonus: 50,
    popular: true,
    description: 'Most popular for serious undergrads & grad students',
    features: [
      '500 study credits (2,500 flashcards/quizzes)',
      '+50 bonus credits (250 extra generations)',
      'Covers full semester workload',
      'Valid for 12 months',
      'Priority support for crunch time',
      'All AI study tools included'
    ]
  },
  {
    id: 'phd-power',
    name: 'PhD Power',
    credits: 1000,
    price: 69.99,
    bonus: 200,
    description: 'Built for graduate students & research powerhouses',
    features: [
      '1,000 study credits (5,000 flashcards/quizzes)',
      '+200 bonus credits (1,000 extra generations)',
      'Handles multiple courses + thesis prep',
      'Valid for 12 months',
      'Priority support',
      'Early access to new AI features',
      'All study tools included'
    ]
  },
  {
    id: 'academic-legend',
    name: 'Academic Legend',
    credits: 2500,
    price: 149.99,
    bonus: 750,
    description: 'Ultimate package for study groups & academic overachievers',
    features: [
      '2,500 study credits (125,000 flashcards/quizzes)',
      '+750 bonus credits (37,500 extra generations)',
      'Perfect for study groups & tutoring',
      'Valid for 18 months',
      'Dedicated academic support',
      'Early access to new features',
      'Custom study integrations',
      'All AI study tools included'
    ]
  }
];

export const CreditPurchase: React.FC<CreditPurchaseProps> = ({
  currentBalance,
  onPurchaseComplete
}) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const { purchaseCredits, refreshAfterPurchase } = useCreditStore();
  const { user } = useAuthStore();

  const handlePurchase = async (packageId: string) => {
    const selectedPkg = creditPackages.find(pkg => pkg.id === packageId);
    if (!selectedPkg) {
      setError('Selected package not found. Please try again.');
      return;
    }

    if (!user?.email) {
      setError('User email not available. Please log out and log back in.');
      return;
    }

    setIsProcessing(true);
    setSelectedPackage(packageId);
    setError(null);
    setSuccess(null);

    try {
      const totalCredits = selectedPkg.credits + (selectedPkg.bonus || 0);
      const result = await purchaseCredits({
        amount: totalCredits,
        credits: totalCredits,
        price: selectedPkg.price,
        email: user.email,
        name: user.name
      });

      if (result.success && result.clientSecret) {
        // TODO: Integrate with Stripe Elements to handle payment
        // For now, we'll show a success message and refresh data
        console.log('Payment intent created:', result.paymentIntentId);
        setSuccess(`Payment intent created successfully for ${selectedPkg.name}! Payment processing will be implemented in the next phase.`);
        setError(null);

        // Refresh credit data
        await refreshAfterPurchase();
        onPurchaseComplete();
      } else {
        setError(result.error || 'Failed to initiate credit purchase. Please try again.');
      }
    } catch (error) {
      console.error('Purchase error:', error);
      if (error instanceof Error) {
        if (error.message.includes('fetch')) {
          setError('Network error. Please check your connection and try again.');
        } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
          setError('Authentication error. Please log out and log back in.');
        } else {
          setError(error.message);
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsProcessing(false);
      setSelectedPackage(null);
    }
  };

  const getPackageIcon = (packageId: string) => {
    switch (packageId) {
      case 'starter':
        return <HiCreditCard className="w-6 h-6" />;
      case 'popular':
        return <HiStar className="w-6 h-6" />;
      case 'power':
        return <HiLightningBolt className="w-6 h-6" />;
      case 'enterprise':
        return <HiSparkles className="w-6 h-6" />;
      default:
        return <HiCreditCard className="w-6 h-6" />;
    }
  };

  const getDiscountPercentage = (credits: number, bonus: number, price: number) => {
    if (!bonus) return null;
    const totalCredits = credits + bonus;
    const regularPrice = (totalCredits / credits) * price;
    const discount = ((regularPrice - price) / regularPrice) * 100;
    return Math.round(discount);
  };

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-400">Error</h3>
              <div className="mt-1 text-sm text-red-300">{error}</div>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => setError(null)}
                className="inline-flex rounded-md bg-red-900/20 p-1.5 text-red-400 hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-900"
              >
                <span className="sr-only">Dismiss</span>
                <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success Display */}
      {success && (
        <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-400">Success</h3>
              <div className="mt-1 text-sm text-green-300">{success}</div>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => setSuccess(null)}
                className="inline-flex rounded-md bg-green-900/20 p-1.5 text-green-400 hover:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-green-900"
              >
                <span className="sr-only">Dismiss</span>
                <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Current Balance Display */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white">Current Balance</h3>
            <p className="text-gray-400">Your available credits</p>
          </div>
          <div className="text-right">
            <span className="text-2xl font-bold text-primary-400">{currentBalance}</span>
            <span className="text-gray-400 ml-2">credits</span>
          </div>
        </div>
      </div>

      {/* Credit Packages */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">Power Up Your Studies</h3>
        <p className="text-gray-400 mb-6">Choose the perfect study package for your academic goals</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {creditPackages.map((pkg, index) => {
            const totalCredits = pkg.credits + (pkg.bonus || 0);
            const discount = pkg.bonus ? getDiscountPercentage(pkg.credits, pkg.bonus, pkg.price) : null;
            const isSelected = selectedPackage === pkg.id;
            const isProcessingThis = isProcessing && isSelected;

            return (
              <motion.div
                key={pkg.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`
                  relative bg-background-secondary rounded-lg p-6 border transition-all duration-200
                  ${pkg.popular 
                    ? 'border-primary-500 ring-2 ring-primary-500/20' 
                    : 'border-border-primary hover:border-border-secondary'
                  }
                  ${isSelected ? 'ring-2 ring-primary-500/50' : ''}
                `}
              >
                {/* Popular Badge */}
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                      Most Popular
                    </div>
                  </div>
                )}

                {/* Discount Badge */}
                {discount && (
                  <div className="absolute -top-2 -right-2">
                    <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      {discount}% OFF
                    </div>
                  </div>
                )}

                <div className="text-center">
                  {/* Icon */}
                  <div className={`inline-flex p-3 rounded-lg mb-4 ${
                    pkg.popular ? 'bg-primary-500/20 text-primary-400' : 'bg-background-tertiary text-gray-400'
                  }`}>
                    {getPackageIcon(pkg.id)}
                  </div>

                  {/* Package Name */}
                  <h4 className="text-lg font-semibold text-white mb-2">{pkg.name}</h4>
                  
                  {/* Credits */}
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-white">{pkg.credits}</span>
                    {pkg.bonus && (
                      <span className="text-green-400 text-sm ml-1">+{pkg.bonus}</span>
                    )}
                    <div className="text-gray-400 text-sm">credits</div>
                    {pkg.bonus && (
                      <div className="text-green-400 text-xs">
                        Total: {totalCredits} credits
                      </div>
                    )}
                  </div>

                  {/* Price */}
                  <div className="mb-4">
                    <span className="text-2xl font-bold text-white">${pkg.price}</span>
                    <div className="text-gray-400 text-sm">
                      ${(pkg.price / totalCredits).toFixed(3)} per credit
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-400 text-sm mb-4">{pkg.description}</p>

                  {/* Features */}
                  <div className="space-y-2 mb-6">
                    {pkg.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm text-gray-300">
                        <HiCheck className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Purchase Button */}
                  <Button
                    onClick={() => handlePurchase(pkg.id)}
                    variant={pkg.popular ? "primary" : "secondary"}
                    className="w-full"
                    isLoading={isProcessingThis}
                    disabled={isProcessing || !user?.email}
                  >
                    {isProcessingThis ? 'Processing...' : !user?.email ? 'Login Required' : `Purchase ${pkg.name}`}
                  </Button>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Additional Information */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h4 className="text-lg font-semibold text-white mb-4">Student-Friendly Payment</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="font-medium text-white mb-2">Safe & Secure</h5>
            <p className="text-gray-400 text-sm">
              Student-safe payments through Stripe. Your payment info is never stored. Perfect for using your student card or parent's card with permission.
            </p>
          </div>
          <div>
            <h5 className="font-medium text-white mb-2">Flexible Validity</h5>
            <p className="text-gray-400 text-sm">
              Credits last for the full duration shown - perfect for semester planning. No rush to use them all at once!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
