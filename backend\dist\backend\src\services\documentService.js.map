{"version": 3, "file": "documentService.js", "sourceRoot": "", "sources": ["../../../../src/services/documentService.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAA4B;AAC5B,sDAA8B;AAC9B,uDAAqD;AAErD,qEAAqE;AACrE,MAAM,mBAAmB,GAAG,IAAA,0BAAY,EACtC,OAAO,CAAC,GAAG,CAAC,YAAa,EACzB,OAAO,CAAC,GAAG,CAAC,yBAA0B,EACtC;IACE,IAAI,EAAE;QACJ,gBAAgB,EAAE,KAAK;QACvB,cAAc,EAAE,KAAK;KACtB;CACF,CACF,CAAC;AAEF,MAAa,iBAAiB;IAC5B,KAAK,CAAC,WAAW,CAAC,IAAyB;QACzC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,KAAK,iBAAiB;gBACpB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,KAAK,yEAAyE;gBAC5E,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,KAAK,2EAA2E;gBAC9E,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,MAAc;QACrC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAA,mBAAG,EAAC,MAAM,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAc;QACtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,iBAAO,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAc;QACtC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAc;QACtC,+CAA+C;QAC/C,0EAA0E;QAC1E,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACtC,gDAAgD;YAChD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC5D,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,aAAa,GAAG,WAAW;qBAC9B,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;qBAC3C,IAAI,CAAC,GAAG,CAAC,CAAC;gBACb,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACvC,CAAC;YACD,OAAO,6CAA6C,CAAC;QACvD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,OAAO,IAAI;aACR,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,gDAAgD;aACrE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,qBAAqB;aAC/C,IAAI,EAAE,CAAC;IACZ,CAAC;CACF;AArED,8CAqEC;AAED,2CAA2C;AAC3C,MAAa,iBAAiB;IAC5B,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,UAAkB,EAAE,QAAgB;QACrE,IAAI,CAAC;YACH,+DAA+D;YAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAmB,CAAC,OAAO;iBACtD,IAAI,CAAC,WAAW,CAAC;iBACjB,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE;gBAC5B,WAAW,EAAE,QAAQ;gBACrB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEL,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,QAAgB;QAC/B,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAmB,CAAC,OAAO;iBAChD,IAAI,CAAC,WAAW,CAAC;iBACjB,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEtB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,sCAAsC;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,QAAgB;QAC/B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,mBAAmB,CAAC,OAAO;iBAC/C,IAAI,CAAC,WAAW,CAAC;iBACjB,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAEpD,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF;AAnDD,8CAmDC"}