"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const documentController_1 = require("../controllers/documentController");
const router = (0, express_1.Router)();
// All document routes require authentication
router.use(auth_1.authenticateToken);
// Document CRUD operations
router.post('/upload', documentController_1.uploadDocument);
router.get('/', documentController_1.getDocuments);
router.get('/search', documentController_1.searchDocuments);
router.get('/:id', documentController_1.getDocument);
router.delete('/:id', documentController_1.deleteDocument);
exports.default = router;
//# sourceMappingURL=documents.js.map