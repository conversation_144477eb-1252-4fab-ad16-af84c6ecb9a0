import React from 'react';
import { motion } from 'framer-motion';
import { BaseDialog } from './BaseDialog';
import { Button } from '../../common/Button';
import { DialogAnnouncer } from './DialogAnnouncer';

interface AlertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  confirmText?: string;
  variant?: 'info' | 'success' | 'warning' | 'error';
}

export const AlertDialog: React.FC<AlertDialogProps> = ({
  isOpen,
  onClose,
  title,
  message,
  confirmText = 'OK',
  variant = 'info'
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'success':
        return (
          <div className="flex-shrink-0 w-10 h-10 mx-auto bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'warning':
        return (
          <div className="flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const getButtonVariant = () => {
    switch (variant) {
      case 'error':
        return 'danger';
      default:
        return 'primary';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  const getAnnouncementMessage = () => {
    const titleText = title ? `${title}: ` : '';
    return `${titleText}${message}`;
  };

  return (
    <>
      <DialogAnnouncer
        message={isOpen ? getAnnouncementMessage() : ''}
        priority={variant === 'error' ? 'assertive' : 'polite'}
      />
      <BaseDialog
        isOpen={isOpen}
        onClose={onClose}
        title={title}
        size="sm"
        closeOnOverlayClick={false}
      >
      <motion.div
        className="text-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          {getIcon()}
        </motion.div>

        <motion.div className="mt-4" variants={itemVariants}>
          <p className="text-white leading-relaxed whitespace-pre-line">
            {message}
          </p>
        </motion.div>

        <motion.div className="mt-6" variants={itemVariants}>
          <Button
            onClick={onClose}
            variant={getButtonVariant()}
            className="w-full"
            autoFocus
          >
            {confirmText}
          </Button>
        </motion.div>
      </motion.div>
    </BaseDialog>
    </>
  );
};
