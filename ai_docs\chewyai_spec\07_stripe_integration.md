# Phase 7: Stripe Integration
**Priority**: HIGH - Payment processing and subscription management
**Dependencies**: Phase 1 (Foundation), Phase 2 (Database Schema), Phase 6 (Credit System)
**Estimated Time**: 5-6 hours

## Overview
Implement comprehensive Stripe integration for payment processing, subscription management, webhook handling, and secure payment flow implementation. Supports student-focused pricing with unlimited Pro subscriptions and academic-themed credit packages.

## Objectives
- Set up Stripe payment processing for credit purchases
- Implement subscription management with automated billing
- Create webhook handling for payment events
- Build secure payment flow with PCI DSS compliance
- Establish subscription lifecycle management

## Tasks

### 6.1 Stripe Configuration & Setup
**Objective**: Configure Stripe for production and development environments

**Environment Configuration**:
```typescript
// backend/src/config/stripe.ts
import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
  typescript: true,
});

export const STRIPE_CONFIG = {
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  successUrl: `${process.env.FRONTEND_URL}/payment/success`,
  cancelUrl: `${process.env.FRONTEND_URL}/payment/cancel`,
};
```

**Product & Price Setup**:
- Credit packages (Study Buddy 100, Dean's List 500+50, Academic Legend 2500+750)
- Subscription tiers (Scholar Pro $9.99/month, Academic Year Pass $99.99/year)
- Recurring billing configuration

### 6.2 Payment Processing Implementation
**Objective**: Secure credit purchase workflow with Stripe Checkout

**API Endpoints**:
- `POST /api/stripe/create-checkout-session` - Create payment session
- `POST /api/stripe/create-subscription` - Start subscription
- `POST /api/stripe/cancel-subscription` - Cancel subscription
- `GET /api/stripe/customer` - Get customer information
- `POST /api/stripe/update-payment-method` - Update payment details

**Checkout Session Creation**:
```typescript
// backend/src/routes/stripe.ts
export const createCheckoutSession = async (req: Request, res: Response) => {
  const { priceId, quantity, userId } = req.body;
  
  try {
    const session = await stripe.checkout.sessions.create({
      customer: await getOrCreateStripeCustomer(userId),
      payment_method_types: ['card'],
      line_items: [{
        price: priceId,
        quantity: quantity,
      }],
      mode: 'payment',
      success_url: `${STRIPE_CONFIG.successUrl}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: STRIPE_CONFIG.cancelUrl,
      metadata: {
        userId,
        creditAmount: calculateCredits(priceId, quantity),
      },
    });
    
    res.json({ sessionId: session.id });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
```

### 6.3 Subscription Management
**Objective**: Automated subscription billing and lifecycle management

**Subscription Operations**:
```typescript
// Subscription creation
export const createSubscription = async (userId: string, priceId: string) => {
  const customer = await getOrCreateStripeCustomer(userId);
  
  const subscription = await stripe.subscriptions.create({
    customer: customer.id,
    items: [{ price: priceId }],
    payment_behavior: 'default_incomplete',
    payment_settings: { save_default_payment_method: 'on_subscription' },
    expand: ['latest_invoice.payment_intent'],
  });
  
  // Store subscription in database
  await db('subscriptions').insert({
    user_id: userId,
    stripe_subscription_id: subscription.id,
    stripe_customer_id: customer.id,
    status: subscription.status,
    current_period_start: new Date(subscription.current_period_start * 1000),
    current_period_end: new Date(subscription.current_period_end * 1000),
  });
  
  return subscription;
};
```

**Subscription Status Management**:
- Active: Full access to subscription benefits
- Past Due: Grace period with limited access
- Canceled: Immediate access revocation
- Incomplete: Payment method required

### 6.4 Webhook Event Handling
**Objective**: Process Stripe events for payment confirmations and subscription updates

**Webhook Endpoint**:
```typescript
// backend/src/routes/webhooks/stripe.ts
export const handleStripeWebhook = async (req: Request, res: Response) => {
  const sig = req.headers['stripe-signature'] as string;
  
  try {
    const event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      STRIPE_CONFIG.webhookSecret
    );
    
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object);
        break;
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      case 'customer.subscription.deleted':
        await handleSubscriptionCanceled(event.data.object);
        break;
    }
    
    res.json({ received: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};
```

**Event Handlers**:
```typescript
// Handle successful credit purchase
const handleCheckoutCompleted = async (session: Stripe.Checkout.Session) => {
  const { userId, creditAmount } = session.metadata!;
  
  await db.transaction(async (trx) => {
    // Add credits to user account
    await trx('user_credits')
      .where('user_id', userId)
      .increment('purchased_credits', parseInt(creditAmount))
      .increment('total_credits', parseInt(creditAmount));
    
    // Record transaction
    await trx('credit_transactions').insert({
      user_id: userId,
      amount: parseInt(creditAmount),
      type: 'purchase',
      description: 'Credit package purchase',
      stripe_session_id: session.id,
    });
  });
};
```

### 6.5 Customer Management
**Objective**: Stripe customer creation and management

**Customer Operations**:
```typescript
// Get or create Stripe customer
export const getOrCreateStripeCustomer = async (userId: string) => {
  const user = await db('users').where('id', userId).first();
  
  if (user.stripe_customer_id) {
    return await stripe.customers.retrieve(user.stripe_customer_id);
  }
  
  const customer = await stripe.customers.create({
    email: user.email,
    metadata: { userId },
  });
  
  await db('users')
    .where('id', userId)
    .update({ stripe_customer_id: customer.id });
  
  return customer;
};
```

## Implementation Files

### Backend Routes
- `backend/src/routes/stripe.ts` - Stripe payment endpoints
- `backend/src/routes/webhooks/stripe.ts` - Webhook event handling
- `backend/src/routes/subscription.ts` - Subscription management

### Services
- `backend/src/services/stripeService.ts` - Core Stripe operations
- `backend/src/services/subscriptionService.ts` - Subscription lifecycle management
- `backend/src/services/customerService.ts` - Customer management

### Configuration
- `backend/src/config/stripe.ts` - Stripe configuration and setup
- `backend/src/config/products.ts` - Product and pricing definitions

### Database
- `backend/src/database/migrations/006_stripe_integration.sql` - Stripe-related tables
- `backend/src/database/migrations/007_subscriptions.sql` - Subscription management tables

### Types
- `shared/types/stripe.ts` - Stripe-related TypeScript interfaces
- `shared/types/subscription.ts` - Subscription type definitions

## Success Criteria
- ✅ Credit purchases process successfully through Stripe Checkout
- ✅ Subscription billing works with automated renewals
- ✅ Webhook events properly update database state
- ✅ Customer management integrates with user accounts
- ✅ Payment failures handled gracefully with retry logic
- ✅ Subscription cancellations process correctly
- ✅ PCI DSS compliance maintained throughout payment flow
- ✅ Error handling provides clear user feedback

## Security Considerations
- All payment processing handled by Stripe (PCI DSS compliant)
- Webhook signature verification prevents unauthorized events
- Customer data encrypted and securely stored
- Payment method details never stored locally
- Subscription status validated on each request

## Integration Points
- **Phase 5**: Credit system receives purchased credits
- **Phase 13**: Enhanced billing interface displays payment history
- **Frontend**: Payment forms and subscription management UI

## Notes
This Stripe integration provides the foundation for all monetization features in ChewyAI's student-focused business model. The webhook system ensures reliable payment processing and subscription management for both unlimited Pro subscriptions and academic-themed credit packages.
