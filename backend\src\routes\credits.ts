import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getCreditBalance,
  getCreditHistory,
  getCreditStats,
  getOperationCosts,
  checkCreditSufficiency,
  purchaseCredits
} from '../controllers/creditController';

const router = Router();

// All credit routes require authentication
router.use(authenticateToken);

// Credit information routes
router.get('/balance', getCreditBalance);
router.get('/history', getCreditHistory);
router.get('/stats', getCreditStats);
router.get('/pricing', getOperationCosts);
router.get('/check/:operationType', checkCreditSufficiency);

// Credit purchase route
router.post('/purchase', purchaseCredits);

export default router;
