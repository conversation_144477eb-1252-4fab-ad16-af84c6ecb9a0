import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useAuthStore } from "../../stores/authStore";
import { Button } from "../common/Button";
import { Input } from "../common/Input";

export const SignupForm: React.FC = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { signup, isLoading } = useAuthStore();
  const navigate = useNavigate();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!formData.email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Email is invalid";
    if (!formData.password) newErrors.password = "Password is required";
    else if (formData.password.length < 6)
      newErrors.password = "Password must be at least 6 characters";
    if (formData.password !== formData.confirmPassword)
      newErrors.confirmPassword = "Passwords do not match";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    const result = await signup(
      formData.email,
      formData.password,
      formData.name || undefined
    );
    if (result.success) navigate("/dashboard");
    else setErrors({ general: result.error || "Signup failed" });
  };

  const updateField = (field: string, value: string) =>
    setFormData((prev) => ({ ...prev, [field]: value }));

  return (
    <div className="min-h-screen flex items-center justify-center bg-background-primary">
      <div className="max-w-md w-full space-y-8 p-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
            Create your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-400">
            Or{" "}
            <Link
              to="/login"
              className="font-medium text-primary-500 hover:text-primary-400"
            >
              sign in to your existing account
            </Link>
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {errors.general && (
            <div className="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded">
              {errors.general}
            </div>
          )}
          <div className="space-y-4">
            <Input
              label="Full Name (Optional)"
              value={formData.name}
              onChange={(v) => updateField("name", v)}
              placeholder="Enter your full name"
            />
            <Input
              label="Email address"
              type="email"
              value={formData.email}
              onChange={(v) => updateField("email", v)}
              error={errors.email}
              placeholder="Enter your email"
              required
            />
            <Input
              label="Password"
              type="password"
              value={formData.password}
              onChange={(v) => updateField("password", v)}
              error={errors.password}
              placeholder="Create a password"
              required
            />
            <Input
              label="Confirm Password"
              type="password"
              value={formData.confirmPassword}
              onChange={(v) => updateField("confirmPassword", v)}
              error={errors.confirmPassword}
              placeholder="Confirm your password"
              required
            />
          </div>
          <Button
            type="submit"
            isLoading={isLoading}
            className="w-full"
            size="lg"
          >
            Create Account
          </Button>
        </form>
      </div>
    </div>
  );
};
