"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const quizQuestionController_1 = require("../controllers/quizQuestionController");
const router = (0, express_1.Router)();
// All quiz question routes require authentication
router.use(auth_1.authenticateToken);
// Quiz question CRUD operations
router.get('/study-set/:studySetId', quizQuestionController_1.getQuizQuestions);
router.post('/study-set/:studySetId', quizQuestionController_1.createQuizQuestion);
router.put('/:id', quizQuestionController_1.updateQuizQuestion);
router.delete('/:id', quizQuestionController_1.deleteQuizQuestion);
exports.default = router;
//# sourceMappingURL=quizQuestions.js.map