import { Request, Response, NextFunction } from "express";
import { supabaseService } from "../services/supabaseService";

declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        profile?: any;
      };
    }
  }
}

export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      return res
        .status(401)
        .json({ success: false, error: "Access token required" });
    }

    const {
      data: { user },
      error,
    } = await supabaseService.verifyToken(token);

    if (error || !user) {
      return res
        .status(401)
        .json({ success: false, error: "Invalid or expired token" });
    }

    const profile = await supabaseService.getUserProfile(user.id);

    if (!profile || !profile.is_active) {
      return res
        .status(401)
        .json({ success: false, error: "User account not found or inactive" });
    }

    req.user = { id: user.id, email: user.email!, profile };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res
      .status(500)
      .json({ success: false, error: "Authentication service error" });
  }
};

export const optionalAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    if (token) {
      const { data: { user }, error } = await supabaseService.verifyToken(token);

      if (!error && user) {
        const profile = await supabaseService.getUserProfile(user.id);
        if (profile && profile.is_active) {
          req.user = {
            id: user.id,
            email: user.email!,
            profile
          };
        }
      }
    }

    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};
