import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/common/Button';
import { Input } from '../components/common/Input';
import { useDialog } from '../contexts/DialogContext';

export const CreateStudySetPage: React.FC = () => {
  const navigate = useNavigate();
  const { alert } = useDialog();
  
  const [studySetName, setStudySetName] = useState('');
  const [studySetType, setStudySetType] = useState<'flashcards' | 'quiz'>('flashcards');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const createStudySet = async (data: { name: string; type: 'flashcards' | 'quiz' }) => {
    const token = localStorage.getItem('auth_token');
    
    const response = await fetch('/api/study-sets', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorResult = await response.json();
      throw new Error(errorResult.error || 'Failed to create study set');
    }

    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!studySetName.trim()) {
      newErrors.name = 'Study set name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const studySet = await createStudySet({
        name: studySetName.trim(),
        type: studySetType
      });

      // Navigate to the newly created study set for editing
      navigate(`/study-sets/${studySet.id}`);
    } catch (error: any) {
      await alert({
        title: 'Error',
        message: error.message || 'Failed to create study set',
        variant: 'error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background-primary text-white">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Create New Study Set</h1>
          <p className="text-gray-400">
            Create an empty study set that you can populate with flashcards or quiz questions
          </p>
        </div>

        {/* Form */}
        <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Study Set Name */}
            <Input
              label="Study Set Name"
              value={studySetName}
              onChange={setStudySetName}
              placeholder="e.g., Biology Chapter 5, History Final Review"
              error={errors.name}
              required
              className="w-full"
            />

            {/* Study Set Type */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                Primary Content Type
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  className={`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${studySetType === 'flashcards'
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                    }
                  `}
                  onClick={() => setStudySetType('flashcards')}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`
                      w-4 h-4 rounded-full border-2 flex-shrink-0
                      ${studySetType === 'flashcards'
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-500'
                      }
                    `}>
                      {studySetType === 'flashcards' && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium text-white">Flashcards</h3>
                      <p className="text-sm text-gray-400">
                        Question and answer cards for memorization
                      </p>
                    </div>
                  </div>
                </div>

                <div
                  className={`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${studySetType === 'quiz'
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                    }
                  `}
                  onClick={() => setStudySetType('quiz')}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`
                      w-4 h-4 rounded-full border-2 flex-shrink-0
                      ${studySetType === 'quiz'
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-500'
                      }
                    `}>
                      {studySetType === 'quiz' && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium text-white">Quiz Questions</h3>
                      <p className="text-sm text-gray-400">
                        Multiple choice, true/false, and short answer questions
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Info Box */}
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5">
                  💡
                </div>
                <div className="text-sm">
                  <p className="text-blue-400 font-medium mb-1">What happens next?</p>
                  <p className="text-blue-300">
                    After creating your study set, you'll be able to add content manually or 
                    use AI to generate flashcards and quiz questions from your documents.
                  </p>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex space-x-4">
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate('/dashboard')}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isLoading}
                className="flex-1"
              >
                Create Study Set
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
