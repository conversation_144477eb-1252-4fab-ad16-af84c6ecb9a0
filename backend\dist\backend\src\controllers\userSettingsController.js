"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserPreferences = exports.getUserPreferences = exports.updateUserSettings = exports.getUserSettings = void 0;
const userSettingsService_1 = require("../services/userSettingsService");
const getUserSettings = async (req, res) => {
    try {
        const userId = req.user.id;
        const settings = await userSettingsService_1.userSettingsService.getUserSettings(userId);
        res.json({
            success: true,
            data: settings
        });
    }
    catch (error) {
        console.error('Get user settings error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to fetch user settings'
        });
    }
};
exports.getUserSettings = getUserSettings;
const updateUserSettings = async (req, res) => {
    try {
        const userId = req.user.id;
        const updates = req.body;
        // Validate request body
        if (!updates || typeof updates !== 'object') {
            return res.status(400).json({
                success: false,
                error: 'Invalid request body'
            });
        }
        // Validate allowed fields
        const allowedFields = ['skip_delete_confirmations', 'shuffle_flashcards'];
        const providedFields = Object.keys(updates);
        const invalidFields = providedFields.filter(field => !allowedFields.includes(field));
        if (invalidFields.length > 0) {
            return res.status(400).json({
                success: false,
                error: `Invalid fields: ${invalidFields.join(', ')}`
            });
        }
        if (providedFields.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No valid fields provided for update'
            });
        }
        const updatedSettings = await userSettingsService_1.userSettingsService.updateUserSettings(userId, updates);
        res.json({
            success: true,
            data: updatedSettings
        });
    }
    catch (error) {
        console.error('Update user settings error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to update user settings'
        });
    }
};
exports.updateUserSettings = updateUserSettings;
const getUserPreferences = async (req, res) => {
    try {
        const userId = req.user.id;
        const preferences = await userSettingsService_1.userPreferencesService.getUserPreferences(userId);
        res.json({
            success: true,
            data: preferences
        });
    }
    catch (error) {
        console.error('Get user preferences error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to fetch user preferences'
        });
    }
};
exports.getUserPreferences = getUserPreferences;
const updateUserPreferences = async (req, res) => {
    try {
        const userId = req.user.id;
        const updates = req.body;
        // Validate request body
        if (!updates || typeof updates !== 'object') {
            return res.status(400).json({
                success: false,
                error: 'Invalid request body'
            });
        }
        // Validate allowed fields
        const allowedFields = ['theme', 'language', 'study_reminders', 'auto_save', 'default_study_mode', 'session_duration', 'difficulty_level'];
        const providedFields = Object.keys(updates);
        const invalidFields = providedFields.filter(field => !allowedFields.includes(field));
        if (invalidFields.length > 0) {
            return res.status(400).json({
                success: false,
                error: `Invalid fields: ${invalidFields.join(', ')}`
            });
        }
        if (providedFields.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No valid fields provided for update'
            });
        }
        const updatedPreferences = await userSettingsService_1.userPreferencesService.updateUserPreferences(userId, updates);
        res.json({
            success: true,
            data: updatedPreferences
        });
    }
    catch (error) {
        console.error('Update user preferences error:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to update user preferences'
        });
    }
};
exports.updateUserPreferences = updateUserPreferences;
//# sourceMappingURL=userSettingsController.js.map