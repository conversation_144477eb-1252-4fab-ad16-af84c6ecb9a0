import React, { createContext, useContext, useState, useCallback } from 'react';
import { AlertDialog, ConfirmDialog, PromptDialog } from '../components/ui/dialogs';
import { NeverAskAgainCheckbox } from '../components/ui/NeverAskAgainCheckbox';

interface AlertOptions {
  title?: string;
  message: string;
  confirmText?: string;
  variant?: 'info' | 'success' | 'warning' | 'error';
}

interface ConfirmOptions {
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'info' | 'warning' | 'danger';
  // Enhanced options
  buttonLayout?: 'default' | 'corners';
  showNeverAskAgain?: boolean;
  onNeverAskAgainChange?: (checked: boolean) => void;
}

interface PromptOptions {
  title?: string;
  message: string;
  placeholder?: string;
  defaultValue?: string;
  confirmText?: string;
  cancelText?: string;
  inputType?: 'text' | 'email' | 'password' | 'number';
  validation?: (value: string) => string | null;
}

interface DialogState {
  type: 'alert' | 'confirm' | 'prompt' | null;
  isOpen: boolean;
  options: AlertOptions | ConfirmOptions | PromptOptions | null;
  resolve: ((value: any) => void) | null;
  isLoading: boolean;
}

interface DialogContextType {
  alert: (options: AlertOptions) => Promise<void>;
  confirm: (options: ConfirmOptions) => Promise<boolean>;
  prompt: (options: PromptOptions) => Promise<string | null>;
  closeDialog: () => void;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

export const useDialog = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  return context;
};

export const DialogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [dialogState, setDialogState] = useState<DialogState>({
    type: null,
    isOpen: false,
    options: null,
    resolve: null,
    isLoading: false
  });

  const [neverAskAgain, setNeverAskAgain] = useState(false);

  const closeDialog = useCallback(() => {
    setDialogState(prev => ({
      ...prev,
      isOpen: false,
      isLoading: false
    }));

    // Clear state after animation
    setTimeout(() => {
      setDialogState({
        type: null,
        isOpen: false,
        options: null,
        resolve: null,
        isLoading: false
      });
      setNeverAskAgain(false); // Reset checkbox state
    }, 200);
  }, []);

  const alert = useCallback((options: AlertOptions): Promise<void> => {
    return new Promise((resolve) => {
      setDialogState({
        type: 'alert',
        isOpen: true,
        options,
        resolve,
        isLoading: false
      });
    });
  }, []);

  const confirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setDialogState({
        type: 'confirm',
        isOpen: true,
        options,
        resolve,
        isLoading: false
      });
    });
  }, []);

  const prompt = useCallback((options: PromptOptions): Promise<string | null> => {
    return new Promise((resolve) => {
      setDialogState({
        type: 'prompt',
        isOpen: true,
        options,
        resolve,
        isLoading: false
      });
    });
  }, []);

  const handleAlertClose = useCallback(() => {
    if (dialogState.resolve) {
      dialogState.resolve(undefined);
    }
    closeDialog();
  }, [dialogState.resolve, closeDialog]);

  const handleConfirmClose = useCallback(() => {
    if (dialogState.resolve) {
      dialogState.resolve(false);
    }
    closeDialog();
  }, [dialogState.resolve, closeDialog]);

  const handleConfirmConfirm = useCallback(() => {
    if (dialogState.resolve) {
      dialogState.resolve(true);
    }
    closeDialog();
  }, [dialogState.resolve, closeDialog]);

  const handlePromptClose = useCallback(() => {
    if (dialogState.resolve) {
      dialogState.resolve(null);
    }
    closeDialog();
  }, [dialogState.resolve, closeDialog]);

  const handlePromptConfirm = useCallback((value: string) => {
    if (dialogState.resolve) {
      dialogState.resolve(value);
    }
    closeDialog();
  }, [dialogState.resolve, closeDialog]);

  const contextValue: DialogContextType = {
    alert,
    confirm,
    prompt,
    closeDialog
  };

  return (
    <DialogContext.Provider value={contextValue}>
      {children}
      
      {/* Alert Dialog */}
      {dialogState.type === 'alert' && dialogState.options && (
        <AlertDialog
          isOpen={dialogState.isOpen}
          onClose={handleAlertClose}
          {...(dialogState.options as AlertOptions)}
        />
      )}
      
      {/* Confirm Dialog */}
      {dialogState.type === 'confirm' && dialogState.options && (() => {
        const confirmOptions = dialogState.options as ConfirmOptions;
        const additionalContent = confirmOptions.showNeverAskAgain ? (
          <NeverAskAgainCheckbox
            checked={neverAskAgain}
            onChange={(checked) => {
              setNeverAskAgain(checked);
              confirmOptions.onNeverAskAgainChange?.(checked);
            }}
            disabled={dialogState.isLoading}
          />
        ) : undefined;

        return (
          <ConfirmDialog
            isOpen={dialogState.isOpen}
            onClose={handleConfirmClose}
            onConfirm={handleConfirmConfirm}
            isLoading={dialogState.isLoading}
            additionalContent={additionalContent}
            {...confirmOptions}
          />
        );
      })()}
      
      {/* Prompt Dialog */}
      {dialogState.type === 'prompt' && dialogState.options && (
        <PromptDialog
          isOpen={dialogState.isOpen}
          onClose={handlePromptClose}
          onConfirm={handlePromptConfirm}
          isLoading={dialogState.isLoading}
          {...(dialogState.options as PromptOptions)}
        />
      )}
    </DialogContext.Provider>
  );
};
