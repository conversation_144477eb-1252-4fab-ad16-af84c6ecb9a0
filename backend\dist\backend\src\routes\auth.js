"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const supabaseService_1 = require("../services/supabaseService");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.post("/signup", async (req, res) => {
    try {
        const { email, password, name } = req.body;
        if (!email || !password) {
            return res
                .status(400)
                .json({ success: false, error: "Email and password are required" });
        }
        const { data: authData, error: authError } = await supabaseService_1.supabase.auth.signUp({
            email,
            password,
            options: { data: { name } },
        });
        if (authError) {
            return res.status(400).json({ success: false, error: authError.message });
        }
        if (!authData.user) {
            return res
                .status(400)
                .json({ success: false, error: "Failed to create user account" });
        }
        const profile = await supabaseService_1.supabaseService.createUserProfile(authData.user.id, email, name);
        const result = {
            success: true,
            user: profile,
            token: authData.session?.access_token,
        };
        res.status(201).json(result);
    }
    catch (error) {
        console.error('Signup error:', error);
        res.status(500).json({ success: false, error: "Internal server error" });
    }
});
router.post("/login", async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res
                .status(400)
                .json({ success: false, error: "Email and password are required" });
        }
        const { data: authData, error: authError } = await supabaseService_1.supabase.auth.signInWithPassword({
            email,
            password,
        });
        if (authError) {
            return res.status(401).json({ success: false, error: authError.message });
        }
        if (!authData.user || !authData.session) {
            return res
                .status(401)
                .json({ success: false, error: "Authentication failed" });
        }
        const profile = await supabaseService_1.supabaseService.getUserProfile(authData.user.id);
        if (!profile) {
            return res
                .status(404)
                .json({ success: false, error: "User profile not found" });
        }
        await supabaseService_1.supabaseService.updateUserProfile(authData.user.id, {
            last_login: new Date().toISOString(),
        });
        const result = {
            success: true,
            user: profile,
            token: authData.session.access_token,
        };
        res.json(result);
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ success: false, error: "Internal server error" });
    }
});
router.get("/user", auth_1.authenticateToken, async (req, res) => {
    try {
        const profile = await supabaseService_1.supabaseService.getUserProfile(req.user.id);
        res.json({ success: true, data: profile });
    }
    catch (error) {
        console.error('Get user error:', error);
        res
            .status(500)
            .json({ success: false, error: "Failed to get user profile" });
    }
});
// Debug endpoint to check token
router.post("/debug-token", async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(" ")[1];
        console.log("Debug - Auth header:", authHeader);
        console.log("Debug - Token:", token ? `${token.substring(0, 20)}...` : "null");
        if (!token) {
            return res.json({
                success: false,
                error: "No token provided",
                authHeader: authHeader || "missing"
            });
        }
        const { data: { user }, error } = await supabaseService_1.supabaseService.verifyToken(token);
        console.log("Debug - Supabase verification error:", error);
        console.log("Debug - Supabase user:", user ? user.id : "null");
        if (error || !user) {
            return res.json({
                success: false,
                error: "Token verification failed",
                supabaseError: error?.message || "Unknown error"
            });
        }
        const profile = await supabaseService_1.supabaseService.getUserProfile(user.id);
        console.log("Debug - Profile found:", !!profile);
        console.log("Debug - Profile active:", profile?.is_active);
        res.json({
            success: true,
            data: {
                userId: user.id,
                email: user.email,
                profileExists: !!profile,
                profileActive: profile?.is_active
            }
        });
    }
    catch (error) {
        console.log("Debug - Exception:", error.message);
        res.status(500).json({ success: false, error: error.message });
    }
});
router.post("/logout", auth_1.authenticateToken, async (_req, res) => {
    try {
        // Supabase handles token invalidation on client side
        res.json({ success: true, message: "Logged out successfully" });
    }
    catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            success: false,
            error: 'Logout failed'
        });
    }
});
// Change password
router.post("/change-password", auth_1.authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user.id;
        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                error: "Current password and new password are required"
            });
        }
        if (newPassword.length < 8) {
            return res.status(400).json({
                success: false,
                error: "New password must be at least 8 characters long"
            });
        }
        // Get user's current email for verification
        const { data: userData, error: userError } = await supabaseService_1.supabase
            .from('users')
            .select('email')
            .eq('id', userId)
            .single();
        if (userError || !userData) {
            return res.status(404).json({
                success: false,
                error: "User not found"
            });
        }
        // Verify current password by attempting to sign in
        const { error: signInError } = await supabaseService_1.supabase.auth.signInWithPassword({
            email: userData.email,
            password: currentPassword
        });
        if (signInError) {
            return res.status(400).json({
                success: false,
                error: "Current password is incorrect"
            });
        }
        // Update password using Supabase Admin API
        const { error: updateError } = await supabaseService_1.supabase.auth.admin.updateUserById(userId, { password: newPassword });
        if (updateError) {
            return res.status(400).json({
                success: false,
                error: updateError.message
            });
        }
        res.json({
            success: true,
            message: "Password updated successfully"
        });
    }
    catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            success: false,
            error: "Internal server error"
        });
    }
});
// Deactivate account
router.post("/deactivate-account", auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const { confirmation } = req.body;
        if (confirmation !== 'DEACTIVATE') {
            return res.status(400).json({
                success: false,
                error: "Invalid confirmation"
            });
        }
        // Update user status to deactivated
        const { error: updateError } = await supabaseService_1.supabase
            .from('users')
            .update({
            is_active: false,
            deactivated_at: new Date().toISOString()
        })
            .eq('id', userId);
        if (updateError) {
            return res.status(400).json({
                success: false,
                error: "Failed to deactivate account"
            });
        }
        res.json({
            success: true,
            message: "Account deactivated successfully"
        });
    }
    catch (error) {
        console.error('Deactivate account error:', error);
        res.status(500).json({
            success: false,
            error: "Internal server error"
        });
    }
});
// Delete account
router.post("/delete-account", auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const { confirmation } = req.body;
        if (confirmation !== 'DELETE FOREVER') {
            return res.status(400).json({
                success: false,
                error: "Invalid confirmation"
            });
        }
        // Delete user data in order (due to foreign key constraints)
        const deleteOperations = [
            // Delete study sessions first
            supabaseService_1.supabase.from('study_sessions').delete().eq('user_id', userId),
            // Delete quiz questions
            supabaseService_1.supabase.from('quiz_questions').delete().eq('user_id', userId),
            // Delete flashcards
            supabaseService_1.supabase.from('flashcards').delete().eq('user_id', userId),
            // Delete study sets
            supabaseService_1.supabase.from('study_sets').delete().eq('user_id', userId),
            // Delete documents
            supabaseService_1.supabase.from('documents').delete().eq('user_id', userId),
            // Delete credit transactions
            supabaseService_1.supabase.from('credit_transactions').delete().eq('user_id', userId),
            // Finally delete user
            supabaseService_1.supabase.from('users').delete().eq('id', userId)
        ];
        for (const operation of deleteOperations) {
            const { error } = await operation;
            if (error) {
                console.error('Delete operation error:', error);
                // Continue with other deletions even if one fails
            }
        }
        // Delete from Supabase Auth
        const { error: authDeleteError } = await supabaseService_1.supabase.auth.admin.deleteUser(userId);
        if (authDeleteError) {
            console.error('Auth delete error:', authDeleteError);
        }
        res.json({
            success: true,
            message: "Account deleted successfully"
        });
    }
    catch (error) {
        console.error('Delete account error:', error);
        res.status(500).json({
            success: false,
            error: "Internal server error"
        });
    }
});
exports.default = router;
//# sourceMappingURL=auth.js.map