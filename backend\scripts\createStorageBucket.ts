import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createDocumentsBucket() {
  try {
    // Check if bucket already exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('Error listing buckets:', listError);
      return;
    }

    const existingBucket = buckets?.find(bucket => bucket.name === 'documents');
    
    if (existingBucket) {
      console.log('✅ Documents bucket already exists');
      return;
    }

    // Create the bucket
    const { data, error } = await supabase.storage.createBucket('documents', {
      public: false,
      fileSizeLimit: 10485760, // 10MB
      allowedMimeTypes: [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      ]
    });

    if (error) {
      console.error('Error creating bucket:', error);
      return;
    }

    console.log('✅ Documents bucket created successfully:', data);

    // Set up RLS policies for the bucket
    const { error: policyError } = await supabase.rpc('create_storage_policy', {
      bucket_name: 'documents',
      policy_name: 'Users can upload their own documents',
      definition: 'auth.uid()::text = (storage.foldername(name))[1]'
    });

    if (policyError) {
      console.warn('Warning: Could not create storage policy:', policyError);
      console.log('You may need to set up storage policies manually in the Supabase dashboard');
    } else {
      console.log('✅ Storage policy created successfully');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createDocumentsBucket();
