"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUploadService = exports.DocumentProcessor = void 0;
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const mammoth_1 = __importDefault(require("mammoth"));
const supabase_js_1 = require("@supabase/supabase-js");
// Create a service role client for storage operations (bypasses RLS)
const supabaseServiceRole = (0, supabase_js_1.createClient)(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});
class DocumentProcessor {
    async processFile(file) {
        switch (file.mimetype) {
            case 'application/pdf':
                return this.processPDF(file.buffer);
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return this.processDocx(file.buffer);
            case 'text/plain':
                return this.processText(file.buffer);
            case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                return this.processPPTX(file.buffer);
            default:
                throw new Error('Unsupported file type');
        }
    }
    async processPDF(buffer) {
        try {
            const data = await (0, pdf_parse_1.default)(buffer);
            return this.cleanText(data.text);
        }
        catch (error) {
            throw new Error(`PDF processing failed: ${error.message}`);
        }
    }
    async processDocx(buffer) {
        try {
            const result = await mammoth_1.default.extractRawText({ buffer });
            return this.cleanText(result.value);
        }
        catch (error) {
            throw new Error(`DOCX processing failed: ${error.message}`);
        }
    }
    async processText(buffer) {
        try {
            const text = buffer.toString('utf-8');
            return this.cleanText(text);
        }
        catch (error) {
            throw new Error(`Text processing failed: ${error.message}`);
        }
    }
    async processPPTX(buffer) {
        // For PPTX, we'll use a simple text extraction
        // In production, consider using a library like 'officegen' or 'node-pptx'
        try {
            // Basic PPTX text extraction (simplified)
            const text = buffer.toString('utf-8');
            // Extract readable text from PPTX XML structure
            const textMatches = text.match(/<a:t[^>]*>([^<]+)<\/a:t>/g);
            if (textMatches) {
                const extractedText = textMatches
                    .map(match => match.replace(/<[^>]+>/g, ''))
                    .join(' ');
                return this.cleanText(extractedText);
            }
            return 'Unable to extract text from PowerPoint file';
        }
        catch (error) {
            throw new Error(`PPTX processing failed: ${error.message}`);
        }
    }
    cleanText(text) {
        return text
            .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
            .replace(/\n\s*\n/g, '\n') // Remove empty lines
            .trim();
    }
}
exports.DocumentProcessor = DocumentProcessor;
// File upload service for Supabase Storage
class FileUploadService {
    async uploadFile(fileName, fileBuffer, mimeType) {
        try {
            // Use service role client to bypass RLS for storage operations
            const { data, error } = await supabaseServiceRole.storage
                .from('documents')
                .upload(fileName, fileBuffer, {
                contentType: mimeType,
                upsert: false
            });
            if (error) {
                throw new Error(`File upload failed: ${error.message}`);
            }
            return data.path;
        }
        catch (error) {
            throw new Error(`Storage upload failed: ${error.message}`);
        }
    }
    async deleteFile(filePath) {
        try {
            const { error } = await supabaseServiceRole.storage
                .from('documents')
                .remove([filePath]);
            if (error) {
                throw new Error(`File deletion failed: ${error.message}`);
            }
        }
        catch (error) {
            console.error('File deletion error:', error);
            // Don't throw here as this is cleanup
        }
    }
    async getFileUrl(filePath) {
        try {
            const { data } = await supabaseServiceRole.storage
                .from('documents')
                .createSignedUrl(filePath, 3600); // 1 hour expiry
            if (!data?.signedUrl) {
                throw new Error('Failed to generate file URL');
            }
            return data.signedUrl;
        }
        catch (error) {
            throw new Error(`URL generation failed: ${error.message}`);
        }
    }
}
exports.FileUploadService = FileUploadService;
//# sourceMappingURL=documentService.js.map