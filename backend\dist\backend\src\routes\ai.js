"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const creditCheck_1 = require("../middleware/creditCheck");
const aiController_1 = require("../controllers/aiController");
const router = express_1.default.Router();
// Test AI service connectivity (no auth required for health check)
router.get('/test-connection', aiController_1.testAIConnection);
// All other routes require authentication
router.use(auth_1.authenticateToken);
// AI Generation endpoints (require credit check)
router.post('/generate-flashcards', (0, creditCheck_1.checkCredits)('flashcard_generation'), aiController_1.generateFlashcards);
router.post('/generate-more-flashcards', (0, creditCheck_1.checkCredits)('flashcard_generation'), aiController_1.generateMoreFlashcards);
router.post('/generate-quiz', (0, creditCheck_1.checkCredits)('quiz_generation'), aiController_1.generateQuiz);
router.post('/generate-more-quiz-questions', (0, creditCheck_1.checkCredits)('quiz_generation'), aiController_1.generateMoreQuizQuestions);
// Study set management endpoints
router.get('/study-sets', aiController_1.getStudySets);
router.get('/study-sets/:studySetId', aiController_1.getStudySetContent);
exports.default = router;
//# sourceMappingURL=ai.js.map